# src_enhanced 目录文件组织计划

## 📂 **建议的目录结构**

```
src_enhanced/
├── core/                    # 核心功能模块
│   ├── __init__.py
│   ├── data_converter.py    # convert_to_fixmorph.py → 数据转换器
│   ├── experiment_runner.py # experiment_runner.py → 实验运行器
│   └── modules/             # 原 modules/ 目录
│       ├── __init__.py
│       ├── checkpoint_manager.py
│       ├── storage_manager.py
│       └── git_manager.py
│
├── experiments/             # 实验相关脚本
│   ├── __init__.py
│   ├── smart_diff_runner.py # run_smart_diff_experiment.py → 智能diff实验
│   └── small_cve_prep.py    # prepare_small_cve.py → 小规模CVE准备
│
├── analysis/                # 分析工具
│   ├── __init__.py
│   ├── performance_comparison.py # test_smart_diff.py → 性能对比分析
│   └── flaw_analysis.py     # 基于 FixMorph_Flaw_Analysis_Report.md 的分析工具
│
├── tools/                   # 辅助工具
│   ├── __init__.py
│   ├── monitor_progress.py  # monitor_progress.py, check_progress.py → 进度监控
│   ├── experiment_launcher.py # run_official_experiment*.py → 实验启动器
│   └── resume_manager.py    # resume_test.py → 恢复管理器
│
├── demos/                   # 示例和演示
│   ├── __init__.py
│   ├── checkpoint_demo.py   # 断点重连演示
│   ├── storage_demo.py      # 存储管理演示
│   └── run_example.py       # 使用示例
│
├── tests/                   # 测试模块
│   ├── __init__.py
│   ├── test_conversion.py   # 数据转换测试
│   ├── quick_test.py        # 快速测试
│   └── test_small_sample.py # 小样本测试
│
├── docs/                    # 文档
│   ├── __init__.py
│   ├── CHECKPOINT_GUIDE.md  # 断点重连指南
│   ├── IMPLEMENTATION_PLAN.md # 实现计划
│   └── README.md            # 使用说明
│
└── config/                  # 配置文件 (新增)
    ├── __init__.py
    └── experiment_configs.py # 实验配置管理
```

## 🔄 **文件移动映射**

### 核心功能 (core/)
- `convert_to_fixmorph.py` → `core/data_converter.py`
- `experiment_runner.py` → `core/experiment_runner.py`
- `modules/` → `core/modules/` (保持不变)

### 实验脚本 (experiments/)
- `../run_smart_diff_experiment.py` → `experiments/smart_diff_runner.py`
- `../prepare_small_cve.py` → `experiments/small_cve_prep.py`

### 分析工具 (analysis/)
- `../test_smart_diff.py` → `analysis/performance_comparison.py`
- `../fixmorph_flaw_analysis_results.json` → `analysis/flaw_analysis_data.json`

### 辅助工具 (tools/)
- `../monitor_progress.py` → `tools/monitor_progress.py`
- `../check_progress.py` → `tools/check_progress.py`
- `../run_official_experiment*.py` → `tools/experiment_launcher.py`
- `../resume_test.py` → `tools/resume_manager.py`
- `../run_complete_test.py` → `tools/complete_test_runner.py`

### 演示示例 (demos/)
- `checkpoint_demo.py` → `demos/checkpoint_demo.py` (保持)
- `storage_demo.py` → `demos/storage_demo.py` (保持)
- `run_example.py` → `demos/run_example.py` (保持)

### 测试模块 (tests/)
- `test_conversion.py` → `tests/test_conversion.py` (保持)
- `quick_test.py` → `tests/quick_test.py` (保持)
- `../test_small_sample.py` → `tests/test_small_sample.py`

### 文档 (docs/)
- `CHECKPOINT_GUIDE.md` → `docs/CHECKPOINT_GUIDE.md` (保持)
- `IMPLEMENTATION_PLAN.md` → `docs/IMPLEMENTATION_PLAN.md` (保持)
- `README.md` → `docs/README.md` (保持)

## 🎯 **整理后的优势**

### 功能清晰
- **核心功能** 集中在 `core/` 目录
- **实验脚本** 统一在 `experiments/` 目录
- **分析工具** 归类到 `analysis/` 目录
- **辅助工具** 放置在 `tools/` 目录

### 便于维护
- 模块化结构，便于独立开发和测试
- 清晰的依赖关系
- 统一的import路径

### 易于扩展
- 新的功能可以很容易地添加到对应目录
- 每个目录都有明确的职责范围

## 🛠 **实施建议**

1. **分批移动**: 先移动核心模块，再移动其他文件
2. **更新import**: 移动后需要更新相关的import路径
3. **测试验证**: 每移动一个模块后进行功能测试
4. **文档更新**: 同步更新相关文档中的文件路径引用

## 📝 **注意事项**

- 移动文件时需要保持功能完整性
- 更新所有相关脚本中的import路径
- 确保相对路径引用正确
- 保持向后兼容性 