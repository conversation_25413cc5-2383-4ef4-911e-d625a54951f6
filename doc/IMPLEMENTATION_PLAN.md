# FixMorph数据转换和实验实施计划

## 📋 项目概述

本计划旨在将用户的enhanced_and_nvd_dataset.json数据转换为FixMorph工具所需的格式，并实现完整的自动化实验流程。

### 数据源分析
- **用户数据**: `/FixMorph/data/enhanced_data/enhanced_and_nvd_dataset.json`
- **数据特点**: 包含CVE信息、Git commit哈希、版本信息等
- **目标**: 转换为FixMorph可处理的目录结构和配置文件

### FixMorph输入要求
- **PA目录**: 修补前的源代码版本（漏洞版本）
- **PB目录**: 修补后的源代码版本（修复版本）
- **PC目录**: 目标移植版本（需要应用补丁的版本）
- **配置文件**: 指定各目录路径、构建命令等

### 支持的项目类型
- **Linux内核项目**: 主要的实验对象
- **其他C/C++项目**: 包含各种开源项目的CVE数据
- **多仓库支持**: 自动识别和处理不同的Git仓库URL

## 🎯 核心数据映射关系

```
用户数据字段                    -> FixMorph格式
versions.source.vulnerable     -> PA目录 (修补前版本)
versions.source.patched        -> PB目录 (修补后版本)  
versions.target.vulnerable     -> PC目录 (目标版本)
versions.target.patched        -> PE目录 (可选，用于验证)
```

## 📈 实施阶段计划

### 阶段1: 数据分析和转换器设计 ✅
**状态**: 已完成
- [x] 分析用户数据格式并与FixMorph要求对比
- [x] 确认数据包含所需的commit信息
- [x] 理解从GitHub仓库克隆对应版本的需求

### 阶段2: 核心转换功能开发 🔄
**状态**: 进行中
- [ ] **设计数据转换脚本架构**
  - 创建主转换脚本 `convert_to_fixmorph.py`
  - 设计CVE数据处理类 `CVEDataConverter`
  - 实现配置管理模块

- [ ] **实现Git仓库克隆和版本切换功能**
  - 自动克隆多种项目仓库（Linux内核、其他C/C++项目）
  - 实现commit版本切换逻辑
  - 处理不同仓库URL和分支
  - 支持多仓库并发处理
  - 优化克隆策略（浅克隆、缓存等）

- [ ] **创建符合FixMorph要求的目录结构**
  - 为每个CVE创建独立工作目录
  - 复制对应commit的源码到PA/PB/PC目录
  - 确保目录结构完全符合FixMorph要求

### 阶段3: 配置生成和批处理 ⏳
- [ ] **生成FixMorph配置文件(.conf)**
  - 根据目录路径自动生成配置文件
  - 根据项目类型设置适用的构建命令
    - Linux内核项目的特殊构建配置
    - 通用C/C++项目的标准构建配置
    - 支持自定义构建命令
  - 处理不同项目的模块路径配置

- [ ] **实现批量处理功能**
  - 支持处理整个数据集
  - 提供进度显示和状态跟踪
  - 支持断点续传功能
  - 实现并行处理以提高效率

- [ ] **实现存储空间管理**
  - 实现动态工作目录创建和清理
  - 设置同时存在的最大工作目录数量限制
  - 添加存储空间监控和告警机制
  - 实现实验完成后的自动清理流程

### 阶段4: 质量保证和测试 ⏳
- [ ] **添加错误处理和日志系统**
  - 详细的错误信息和调试日志
  - 处理Git操作失败情况
  - 验证生成的目录结构完整性
  - 实现数据完整性检查

- [ ] **测试转换脚本并验证FixMorph运行**
  - 选择代表性CVE样本进行测试
  - 验证FixMorph能成功运行转换后的数据
  - 调试和修复发现的问题
  - 性能优化和资源使用优化

### 阶段5: 完整实验流程自动化 ⏳
- [ ] **编写完整实验运行脚本**
  - 集成数据转换和FixMorph执行
  - 自动化整个实验流程
  - 支持实验参数配置
  - 实现实验状态管理

- [ ] **实现结果收集和分析工具**
  - 收集FixMorph输出结果
  - 生成实验统计报告
  - 分析成功率和失败原因
  - 提供可视化分析结果

## 🛠️ 技术实现细节

### 核心组件设计

#### 1. 数据转换器 (CVEDataConverter)
```python
class CVEDataConverter:
    def __init__(self, input_data_path, output_base_path)
    def load_cve_data(self)
    def convert_single_cve(self, cve_item)
    def detect_project_type(self, repo_url)  # 检测项目类型
    def clone_repository(self, repo_url, commit_hash, target_dir)
    def generate_config_file(self, cve_item, paths, project_type)
```

#### 2. Git操作管理器 (GitManager)
```python
class GitManager:
    def __init__(self, cache_dir, shared_repos_base_path)
    def get_or_create_shared_repo(self, repo_url)  # 获取或创建共享仓库
    def clone_repo(self, repo_url, target_dir, commit_hash)
    def checkout_commit(self, repo_dir, commit_hash)
    def create_working_copy(self, repo_url, commit_hash, target_dir)  # 从共享仓库创建工作副本
    def cleanup_working_copy(self, target_dir)  # 清理工作目录
    def optimize_clone(self)  # 浅克隆、缓存等优化
    def get_repo_cache_path(self, repo_url)  # 获取仓库缓存路径
```

#### 3. 实验运行器 (ExperimentRunner)
```python
class ExperimentRunner:
    def __init__(self, converted_data_path, max_concurrent_experiments=3)
    def run_single_experiment(self, cve_id)
    def run_batch_experiments(self, cve_list)
    def collect_results(self)
    def cleanup_after_experiment(self, cve_id)  # 实验后清理
    def monitor_storage_usage(self)  # 监控存储空间
```

#### 4. 存储空间管理器 (StorageManager)
```python
class StorageManager:
    def __init__(self, base_path, max_storage_gb=50)
    def check_available_space(self)
    def cleanup_completed_experiments(self)
    def get_directory_size(self, path)
    def enforce_storage_limits(self)
```

### 关键挑战和解决方案

#### 1. 大型仓库克隆优化
**挑战**: Linux内核及其他大型项目仓库很大，完整克隆耗时长
**解决方案**: 
- 使用浅克隆 (`--depth=1`)
- 实现多仓库本地缓存机制
- 只克隆指定commit相关的历史
- 根据仓库大小自动调整克隆策略

#### 2. Commit版本管理
**挑战**: commit可能在不同分支，需要处理分支切换
**解决方案**:
- 使用 `git fetch` 获取特定commit
- 实现智能分支检测和切换
- 处理orphan commits情况

#### 3. 构建配置适配
**挑战**: 不同项目和版本的构建配置差异很大
**解决方案**:
- 实现项目类型自动检测机制
- 为不同项目类型提供相应的构建配置模板
  - Linux内核项目的特殊构建配置
  - 通用C/C++项目的标准构建配置 (make, cmake, autotools等)
  - 支持skip构建选项用于简单项目
- 根据版本和项目类型动态调整构建参数
- 提供fallback构建选项

#### 4. 存储空间管理 ⚠️ **重要考虑**
**挑战**: 每个CVE需要3个版本(PA/PB/PC)，Linux内核及其他仓库很大，会导致存储空间开销巨大
**解决方案**:
- **多仓库管理**: 
  - 为每个不同的项目仓库维护一个共享源仓库
  - 支持Linux内核、其他C/C++项目的并行管理
  - 实现仓库URL到本地缓存的映射机制
- **动态创建工作目录**: PA/PB/PC目录通过Git命令从对应源仓库动态生成
- **及时清理机制**: 单个CVE实验完成后立即删除PA/PB/PC目录
- **批处理优化**: 
  - 实现CVE处理的队列机制
  - 控制同时存在的工作目录数量
  - 支持不同项目的并行实验处理
  - 支持实验完成后的自动清理
- **存储监控**: 监控磁盘使用情况，防止空间不足

### 目录结构设计

```
/FixMorph/src_enhanced/
├── IMPLEMENTATION_PLAN.md          # 本计划文档
├── convert_to_fixmorph.py         # 主转换脚本
├── modules/
│   ├── __init__.py
│   ├── data_converter.py          # CVE数据转换器
│   ├── git_manager.py             # Git操作管理
│   ├── config_generator.py        # 配置文件生成器
│   ├── experiment_runner.py       # 实验运行器
│   └── storage_manager.py         # 存储空间管理器
├── config/
│   ├── default_settings.json      # 默认配置
│   ├── project_configs/           # 项目类型配置
│   │   ├── linux_kernel.json      # Linux内核构建配置
│   │   ├── general_cpp.json       # 通用C/C++项目配置
│   │   └── project_templates.json # 项目类型检测规则
│   └── storage_config.json        # 存储管理配置
├── utils/
│   ├── __init__.py
│   ├── logger.py                  # 日志工具
│   ├── file_utils.py              # 文件操作工具
│   ├── progress_tracker.py        # 进度跟踪
│   └── disk_monitor.py            # 磁盘空间监控
└── tests/
    ├── test_converter.py          # 转换器测试
    ├── test_git_manager.py        # Git管理器测试
    ├── test_storage_manager.py    # 存储管理器测试
    └── sample_data/               # 测试数据样本
```

## 📊 预期成果

### 输出文件结构
```
/FixMorph/experiments/enhanced_dataset/
├── shared_repos/                  # 共享的源仓库集合
│   ├── linux_kernel/              # Linux内核仓库缓存
│   ├── repo_hash_1/               # 其他项目仓库缓存
│   └── repo_hash_2/               # 基于仓库URL hash命名
├── active_experiments/            # 当前进行中的实验目录
│   ├── cve_1/                     # 实验完成后会被自动清理
│   │   ├── PA/                    # 漏洞版本源码 (临时)
│   │   ├── PB/                    # 修复版本源码 (临时)
│   │   ├── PC/                    # 目标版本源码 (临时)
│   │   └── repair.conf            # FixMorph配置文件
│   └── cve_2/
│       └── ...
├── experiment_config.json         # 全局实验配置
├── storage_config.json            # 存储管理配置
└── results/
    ├── success_list.txt           # 成功案例列表
    ├── failure_list.txt           # 失败案例列表
    ├── experiment_report.json     # 详细实验报告
    └── outputs/                   # 保留的重要输出文件
        ├── cve_1_output/          # 每个CVE的结果输出
        └── cve_2_output/
```

### 预期能力
1. **自动化数据转换**: 一键将用户数据转换为FixMorph格式
2. **多项目支持**: 支持Linux内核及其他C/C++项目的CVE数据
3. **批量实验执行**: 支持处理数百个CVE的大规模实验
4. **智能构建配置**: 根据项目类型自动选择合适的构建方式
5. **结果分析**: 提供详细的成功率统计和失败原因分析
6. **可重现性**: 完全自动化的实验流程，确保结果可重现

## 🚀 启动指南

### 快速开始
1. 运行数据转换: `python convert_to_fixmorph.py --input data/enhanced_data/enhanced_and_nvd_dataset.json`
2. 执行单个实验: `python experiment_runner.py --cve-id CVE-2018-1118`
3. 批量实验: `python experiment_runner.py --batch --max-workers 4`

### 配置选项
- `--output-dir`: 指定输出目录
- `--cache-dir`: 指定Git缓存目录
- `--max-workers`: 并行处理数量
- `--timeout`: 单个实验超时时间
- `--debug`: 启用调试模式
- `--max-storage-gb`: 最大存储空间限制 (默认100GB)
- `--auto-cleanup`: 启用实验完成后自动清理
- `--keep-successful`: 保留成功实验的结果目录

## 📝 进度跟踪

- [x] 阶段1: 数据分析和需求理解
- [ ] 阶段2: 核心转换功能开发
- [ ] 阶段3: 配置生成和批处理
- [ ] 阶段4: 质量保证和测试
- [ ] 阶段5: 完整实验流程自动化

---

**更新时间**: 2025-06-16
**负责人**: AI Assistant
**状态**: 设计阶段 -> 开发阶段 