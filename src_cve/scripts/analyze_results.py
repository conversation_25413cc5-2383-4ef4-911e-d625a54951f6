#!/usr/bin/env python3
"""
analyze_results.py

分析 FixMorph CVE 实验结果的工具脚本。

功能：
- 统计实验成功率
- 生成结果汇总报告  
- 提取关键指标和比较结果
- 分析失败原因

使用方法：
    python3 analyze_results.py [--results-dir=PATH] [--output=report.md]
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import argparse


def get_default_results_dir() -> str:
    """获取默认的结果目录路径"""
    script_dir = Path(__file__).parent
    return str(script_dir.parent / "results")


def load_cve_metadata(data_dir: str) -> Dict:
    """加载CVE元数据"""
    cve_data_file = os.path.join(data_dir, "cve", "cve-data.json")
    if not os.path.exists(cve_data_file):
        raise FileNotFoundError("CVE data file not found: " + cve_data_file)
    
    with open(cve_data_file, 'r') as f:
        return json.load(f)


def analyze_single_experiment(exp_dir: str, exp_id: int) -> Dict:
    """分析单个实验的结果"""
    result = {
        "id": exp_id,
        "success": False,
        "identical": False,
        "build_error": False,
        "runtime_error": False,
        "comparison_result": "UNKNOWN",
        "execution_time": None,
        "error_message": None
    }
    
    # 检查基本文件存在性
    log_latest = os.path.join(exp_dir, "log-latest")
    log_error = os.path.join(exp_dir, "log-error")
    output_dir = os.path.join(exp_dir, "output")
    
    if not os.path.exists(log_latest):
        result["error_message"] = "Missing main log file"
        return result
    
    # 分析主日志
    try:
        with open(log_latest, 'r') as f:
            log_content = f.read()
            
        if "FixMorph finished successfully" in log_content:
            result["success"] = True
            
        # 提取执行时间
        for line in log_content.split('\n'):
            if "finished successfully after" in line:
                try:
                    time_str = line.split("after")[1].split("minutes")[0].strip()
                    result["execution_time"] = float(time_str)
                except:
                    pass
                    
    except Exception as e:
        result["error_message"] = "Failed to read log: " + str(e)
    
    # 检查错误日志
    if os.path.exists(log_error) and os.path.getsize(log_error) > 0:
        result["runtime_error"] = True
        try:
            with open(log_error, 'r') as f:
                error_content = f.read()[:500]  # 只取前500字符
                result["error_message"] = error_content
        except:
            pass
    
    # 分析输出结果
    comparison_file = os.path.join(output_dir, "comparison-result")
    if os.path.exists(comparison_file):
        try:
            with open(comparison_file, 'r') as f:
                comparison_result = f.read().strip()
                result["comparison_result"] = comparison_result
                if comparison_result == "IDENTICAL":
                    result["identical"] = True
        except:
            pass
    
    return result


def generate_summary_report(results: List[Dict], cve_metadata: List[Dict], output_file: str):
    """生成汇总报告"""
    total_experiments = len(results)
    successful = sum(1 for r in results if r["success"])
    identical = sum(1 for r in results if r["identical"])
    build_errors = sum(1 for r in results if r["build_error"])
    runtime_errors = sum(1 for r in results if r["runtime_error"])
    
    # 创建CVE映射
    cve_map = {item["id"]: item for item in cve_metadata}
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# FixMorph CVE 实验结果汇总报告\n\n")
        f.write("**生成时间**: " + os.popen('date').read().strip() + "\n\n")
        
        f.write("## 📊 总体统计\n\n")
        f.write("- **实验总数**: " + str(total_experiments) + "\n")
        f.write("- **成功实验**: {} ({:.1f}%)\n".format(successful, successful/total_experiments*100))
        f.write("- **完全一致**: {} ({:.1f}%)\n".format(identical, identical/total_experiments*100))
        f.write("- **构建错误**: " + str(build_errors) + "\n")
        f.write("- **运行时错误**: " + str(runtime_errors) + "\n\n")
        
        # 成功率统计
        if successful > 0:
            f.write("## ✅ 成功案例详情\n\n")
            f.write("| 实验ID | CVE编号 | 比较结果 | 执行时间(分钟) |\n")
            f.write("|--------|---------|----------|----------------|\n")
            
            for result in results:
                if result["success"]:
                    exp_id = result["id"]
                    cve_info = cve_map.get(exp_id, {})
                    cve_id = cve_info.get("cve-id", "未知")
                    exec_time = result["execution_time"] or "未知"
                    f.write("| {} | {} | {} | {} |\n".format(exp_id, cve_id, result['comparison_result'], exec_time))
            f.write("\n")
        
        # 失败案例
        failed_results = [r for r in results if not r["success"]]
        if failed_results:
            f.write("## ❌ 失败案例详情\n\n")
            f.write("| 实验ID | CVE编号 | 错误类型 | 错误信息 |\n")
            f.write("|--------|---------|----------|----------|\n")
            
            for result in failed_results:
                exp_id = result["id"]
                cve_info = cve_map.get(exp_id, {})
                cve_id = cve_info.get("cve-id", "未知")
                error_type = []
                if result["build_error"]:
                    error_type.append("构建错误")
                if result["runtime_error"]:
                    error_type.append("运行时错误")
                if not error_type:
                    error_type.append("其他错误")
                
                error_msg = result["error_message"] or "无详细信息"
                error_msg = error_msg.replace('\n', ' ')[:100] + "..." if len(error_msg) > 100 else error_msg
                
                f.write("| {} | {} | {} | {} |\n".format(exp_id, cve_id, '/'.join(error_type), error_msg))
            f.write("\n")
        
        # 执行时间分析
        exec_times = [r["execution_time"] for r in results if r["execution_time"] is not None]
        if exec_times:
            f.write("## ⏱️ 执行时间分析\n\n")
            f.write("- **平均执行时间**: {:.3f} 分钟\n".format(sum(exec_times)/len(exec_times)))
            f.write("- **最短执行时间**: {:.3f} 分钟\n".format(min(exec_times)))
            f.write("- **最长执行时间**: {:.3f} 分钟\n\n".format(max(exec_times)))


def main():
    parser = argparse.ArgumentParser(description="分析 FixMorph CVE 实验结果")
    parser.add_argument("--results-dir", default=None, 
                       help="实验结果目录路径")
    parser.add_argument("--data-dir", default=None,
                       help="数据目录路径")
    parser.add_argument("--output", default="results_summary.md",
                       help="输出报告文件名")
    
    args = parser.parse_args()
    
    # 确定目录路径
    if args.results_dir is None:
        results_dir = get_default_results_dir()
    else:
        results_dir = args.results_dir
        
    if args.data_dir is None:
        script_dir = Path(__file__).parent
        data_dir = str(script_dir.parent / "data")
    else:
        data_dir = args.data_dir
    
    if not os.path.exists(results_dir):
        print("Error: Results directory does not exist: " + results_dir)
        sys.exit(1)
    
    print("Analysis results directory: " + results_dir)
    print("Data directory: " + data_dir)
    
    # 加载CVE元数据
    try:
        cve_metadata = load_cve_metadata(data_dir)
        print("Loaded {} CVE metadata entries".format(len(cve_metadata)))
    except FileNotFoundError as e:
        print("Warning: " + str(e))
        cve_metadata = []
    
    # 分析所有实验结果
    results = []
    experiment_dirs = [d for d in os.listdir(results_dir) 
                      if d.isdigit() and os.path.isdir(os.path.join(results_dir, d))]
    experiment_dirs.sort(key=int)
    
    print("Found {} experiment result directories".format(len(experiment_dirs)))
    
    for exp_dir_name in experiment_dirs:
        exp_id = int(exp_dir_name)
        exp_dir = os.path.join(results_dir, exp_dir_name)
        
        print("Analyzing experiment {}...".format(exp_id))
        result = analyze_single_experiment(exp_dir, exp_id)
        results.append(result)
    
    # 生成报告
    output_file = os.path.join(os.path.dirname(results_dir), args.output)
    generate_summary_report(results, cve_metadata, output_file)
    
    print("\nReport generated: " + output_file)
    
    # 简单统计输出
    total = len(results)
    successful = sum(1 for r in results if r["success"])
    identical = sum(1 for r in results if r["identical"])
    
    print("\nSummary Statistics:")
    print("Total experiments: " + str(total))
    print("Successful: {} ({:.1f}%)".format(successful, successful/total*100))
    print("Identical: {} ({:.1f}%)".format(identical, identical/total*100))


if __name__ == "__main__":
    main() 