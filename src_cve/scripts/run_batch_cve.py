#!/usr/bin/env python3
"""
run_batch_cve.py

批量运行 FixMorph CVE 实验的便捷工具。

功能：
- 支持运行指定范围的CVE实验
- 自动调用原始的driver脚本
- 提供进度显示和错误处理
- 支持续跑功能

使用方法：
    python3 run_batch_cve.py --start=1 --end=5      # 运行实验1-5
    python3 run_batch_cve.py --id=1                 # 运行单个实验
    python3 run_batch_cve.py --all                  # 运行所有实验
    python3 run_batch_cve.py --resume               # 续跑未完成的实验
"""

import os
import sys
import subprocess
import json
import argparse
from pathlib import Path
from typing import List, Set


def get_project_root() -> str:
    """获取FixMorph项目根目录"""
    script_dir = Path(__file__).parent
    return str(script_dir.parent.parent)


def load_cve_metadata() -> List[dict]:
    """加载CVE元数据，获取所有可用的实验ID"""
    project_root = get_project_root()
    cve_data_file = os.path.join(project_root, "src_cve", "data", "cve", "cve-data.json")
    
    if not os.path.exists(cve_data_file):
        # 尝试从原始位置加载
        cve_data_file = os.path.join(project_root, "experiments", "ISSTA21", "cve-data.json")
    
    if not os.path.exists(cve_data_file):
        raise FileNotFoundError("找不到CVE数据文件")
    
    with open(cve_data_file, 'r') as f:
        return json.load(f)


def get_available_experiment_ids() -> List[int]:
    """获取所有可用的实验ID列表"""
    metadata = load_cve_metadata()
    return sorted([item["id"] for item in metadata])


def get_completed_experiments() -> Set[int]:
    """获取已完成的实验ID集合"""
    project_root = get_project_root()
    results_dir = os.path.join(project_root, "src_cve", "results")
    
    completed = set()
    if not os.path.exists(results_dir):
        return completed
    
    for item in os.listdir(results_dir):
        if item.isdigit():
            exp_id = int(item)
            exp_dir = os.path.join(results_dir, item)
            log_file = os.path.join(exp_dir, "log-latest")
            
            # 检查是否成功完成
            if os.path.exists(log_file):
                try:
                    with open(log_file, 'r') as f:
                        log_content = f.read()
                        if "FixMorph finished successfully" in log_content:
                            completed.add(exp_id)
                except:
                    pass
    
    return completed


def run_single_experiment(exp_id: int, only_setup: bool = False) -> bool:
    """运行单个实验"""
    project_root = get_project_root()
    driver_script = os.path.join(project_root, "src_cve", "exp-data", "run_cve_replication.py")
    
    cmd = [sys.executable, driver_script, f"--bug-id={exp_id}"]
    if only_setup:
        cmd.append("--only-setup")
    
    print(f"\n🚀 运行实验 {exp_id}...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 运行实验
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"✅ 实验 {exp_id} 完成成功")
            return True
        else:
            print(f"❌ 实验 {exp_id} 执行失败，返回码: {result.returncode}")
            return False
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断了实验 {exp_id}")
        return False
    except Exception as e:
        print(f"❌ 实验 {exp_id} 执行出错: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description="批量运行 FixMorph CVE 实验")
    parser.add_argument("--start", type=int, help="起始实验ID")
    parser.add_argument("--end", type=int, help="结束实验ID")
    parser.add_argument("--id", type=int, help="运行单个实验ID")
    parser.add_argument("--all", action="store_true", help="运行所有实验")
    parser.add_argument("--resume", action="store_true", help="续跑未完成的实验")
    parser.add_argument("--only-setup", action="store_true", help="仅设置实验环境，不运行")
    parser.add_argument("--force", action="store_true", help="强制重新运行已完成的实验")
    
    args = parser.parse_args()
    
    try:
        # 获取可用的实验ID
        available_ids = get_available_experiment_ids()
        print(f"发现 {len(available_ids)} 个可用实验: {available_ids}")
        
        # 确定要运行的实验ID列表
        target_ids = []
        
        if args.id is not None:
            target_ids = [args.id]
        elif args.start is not None and args.end is not None:
            target_ids = [i for i in available_ids if args.start <= i <= args.end]
        elif args.all:
            target_ids = available_ids
        elif args.resume:
            completed = get_completed_experiments()
            target_ids = [i for i in available_ids if i not in completed]
            print(f"已完成实验: {sorted(completed)}")
            print(f"待运行实验: {target_ids}")
        else:
            parser.print_help()
            return
        
        if not target_ids:
            print("没有需要运行的实验")
            return
        
        # 检查跳过已完成的实验
        if not args.force and not args.resume:
            completed = get_completed_experiments()
            if completed:
                original_count = len(target_ids)
                target_ids = [i for i in target_ids if i not in completed]
                skipped_count = original_count - len(target_ids)
                if skipped_count > 0:
                    print(f"⏩ 跳过 {skipped_count} 个已完成的实验 (使用 --force 强制重新运行)")
        
        if not target_ids:
            print("所有指定的实验都已完成")
            return
        
        print(f"\n📋 计划运行 {len(target_ids)} 个实验: {target_ids}")
        
        # 确认继续
        if len(target_ids) > 1:
            try:
                response = input("\n是否继续? (y/N): ").strip().lower()
                if response not in ['y', 'yes']:
                    print("用户取消运行")
                    return
            except KeyboardInterrupt:
                print("\n用户取消运行")
                return
        
        # 执行实验
        successful = 0
        failed = 0
        
        for i, exp_id in enumerate(target_ids, 1):
            print(f"\n{'='*60}")
            print(f"进度: {i}/{len(target_ids)} - 实验 {exp_id}")
            print(f"{'='*60}")
            
            success = run_single_experiment(exp_id, args.only_setup)
            if success:
                successful += 1
            else:
                failed += 1
                
                # 询问是否继续
                if failed >= 3 and i < len(target_ids):
                    try:
                        response = input(f"\n已有 {failed} 个实验失败，是否继续运行剩余实验? (y/N): ").strip().lower()
                        if response not in ['y', 'yes']:
                            print("用户选择停止运行")
                            break
                    except KeyboardInterrupt:
                        print("\n用户中断运行")
                        break
        
        # 输出总结
        print(f"\n{'='*60}")
        print(f"📊 批量运行完成")
        print(f"{'='*60}")
        print(f"成功: {successful}")
        print(f"失败: {failed}")
        print(f"总计: {successful + failed}")
        
        if successful > 0:
            print(f"\n✅ 可以使用以下命令查看结果:")
            print(f"cd {get_project_root()}/src_cve")
            print(f"python3 scripts/analyze_results.py")
        
    except FileNotFoundError as e:
        print(f"错误: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n用户中断程序运行")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 