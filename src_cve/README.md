# FixMorph CVE 数据集实验整理

这个目录包含了 FixMorph 在 CVE 数据集上的完整实验数据、结果和相关工具。

## 📁 目录结构

```
src_cve/
├── data/                    # 实验数据
│   └── cve/                
│       ├── cve-data.json           # CVE 数据集元数据
│       ├── cve-data-backup.json    # 备份数据
│       └── Results.xlsx            # 官方结果表格
├── scripts/                 # 工具脚本
│   ├── analyze_results.py          # 结果分析工具
│   └── run_batch_cve.py           # 批量运行工具
├── results/                 # 实验结果
│   ├── 1/                          # 实验1结果
│   │   ├── log-latest              # 主要日志
│   │   ├── log-error               # 错误日志
│   │   ├── log-make                # 编译日志
│   │   ├── log-command             # 命令日志
│   │   └── output/                 # 输出文件
│   │       ├── comparison-result   # 比较结果
│   │       ├── orig-diff           # 原始补丁
│   │       ├── transplant-diff     # 移植补丁差异
│   │       └── ...                 # 其他输出文件
│   ├── 2/                          # 实验2结果
│   └── ...                         # 更多实验结果
├── docs/                    # 文档
│   └── original-experiment-readme.md
├── config/                  # 配置文件
├── exp-data/               # 原始实验脚本(保持兼容)
│   ├── run_cve_replication.py     # 单实验入口脚本
│   └── EXPERIMENT_REPORT.md       # 详细实验报告
└── README.md               # 本文件
```

## 🚀 快速开始

### 1. 分析现有结果

查看所有实验的汇总统计：

```bash
cd /FixMorph/src_cve
python3 scripts/analyze_results.py
```

这会生成一个详细的分析报告 `results_summary.md`。

### 2. 运行单个实验

使用原始脚本运行单个CVE实验：

```bash
# 运行实验1 (CVE-2018-1118)
python3 exp-data/run_cve_replication.py --bug-id=1

# 仅设置环境，不运行
python3 exp-data/run_cve_replication.py --bug-id=1 --only-setup
```

### 3. 批量运行实验

使用便捷的批量工具：

```bash
# 运行实验1-5
python3 scripts/run_batch_cve.py --start=1 --end=5

# 运行单个实验
python3 scripts/run_batch_cve.py --id=1

# 续跑未完成的实验
python3 scripts/run_batch_cve.py --resume

# 运行所有实验
python3 scripts/run_batch_cve.py --all
```

## 📊 实验数据说明

### CVE 数据集

`data/cve/cve-data.json` 包含19个CVE的元数据，每个条目包含：

- `id`: 实验编号
- `cve-id`: CVE编号 
- `pa`: 主线版本修复前提交
- `pb`: 主线版本修复后提交
- `pc`: 目标版本修复前提交
- `pe`: 目标版本修复后提交（人工参考）
- `ma`/`mc`: 编译目标模块

### 实验结果结构

每个实验的结果保存在 `results/{实验ID}/` 目录下：

#### 日志文件
- `log-latest`: 主要运行日志，包含完整的执行过程
- `log-error`: 错误日志
- `log-make`: 编译日志  
- `log-command`: 执行的命令记录

#### 输出文件 (`output/` 目录)
- `comparison-result`: 比较结果 (`IDENTICAL`/`DIFFERENT`/等)
- `orig-diff`: 原始补丁内容
- `transplant-diff`: FixMorph生成补丁与人工补丁的差异
- `*-generated-patch`: FixMorph生成的实际补丁
- `vector-map`: 函数/文件映射信息
- `namespace-map-*`: 变量映射信息

## 🛠️ 工具脚本说明

### analyze_results.py

分析实验结果并生成汇总报告：

```bash
python3 scripts/analyze_results.py [选项]

选项:
  --results-dir PATH    指定结果目录路径
  --data-dir PATH       指定数据目录路径  
  --output FILE         输出报告文件名 (默认: results_summary.md)
```

**输出报告包含：**
- 总体成功率统计
- 成功案例详情表格
- 失败案例错误分析
- 执行时间分析

### run_batch_cve.py

批量运行CVE实验：

```bash
python3 scripts/run_batch_cve.py [选项]

选项:
  --start N --end M     运行实验N到M
  --id N                运行单个实验N
  --all                 运行所有实验
  --resume              续跑未完成的实验
  --only-setup          仅设置环境，不运行
  --force               强制重新运行已完成的实验
```

**特性：**
- 自动跳过已完成的实验
- 进度显示和错误处理
- 支持用户中断和续跑
- 失败重试询问机制

## 📈 结果解读

### 成功标准

实验被认为成功需要满足：
1. FixMorph正常完成执行 (`log-latest` 包含 "finished successfully")
2. 生成了比较结果文件 (`comparison-result` 存在)

### 比较结果类型

- `IDENTICAL`: FixMorph生成的补丁与开发者手工补丁完全一致 ✅
- `DIFFERENT`: 补丁内容不同，但可能语义等价 ⚠️ 
- `FAILED`: 补丁生成失败 ❌

### transplant-diff 文件说明

`transplant-diff` 文件记录FixMorph应用补丁前后的差异：
- **文件为空**: 表示生成的补丁与人工补丁完全一致（最佳情况）
- **文件有内容**: 显示两个版本之间的具体差异

## 🔍 常见问题

### Q: transplant-diff 为空是错误吗？
A: 不是！这实际上是**成功的最高标准**，表明FixMorph生成的修复版本与开发者手工修复版本逐字节相同。

### Q: 如何查看特定实验的详细信息？
A: 查看 `results/{实验ID}/log-latest` 文件，包含完整的执行日志。

### Q: 实验失败了怎么办？
A: 
1. 检查 `log-error` 文件查看错误信息
2. 确保Linux内核仓库已正确克隆
3. 检查磁盘空间和网络连接
4. 使用 `--force` 重新运行

### Q: 如何添加新的CVE实验？
A: 
1. 在 `data/cve/cve-data.json` 中添加新条目
2. 确保提供正确的提交哈希和模块信息
3. 使用批量工具运行新实验

## 📝 相关文档

- [`exp-data/EXPERIMENT_REPORT.md`](exp-data/EXPERIMENT_REPORT.md): CVE-2018-1118详细实验报告
- [`docs/original-experiment-readme.md`](docs/original-experiment-readme.md): 原始ISSTA21实验说明
- [FixMorph主文档](../../README.md): 工具主要文档
- [使用手册](../../doc/Manual.md): 详细使用说明

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 查看现有的实验日志和结果
2. 提交具体的错误报告或改进建议
3. 遵循现有的目录结构和命名约定

---

**最后更新**: 2025-06-11  
**数据集**: ISSTA21 CVE实验集 (19个CVE案例)  
**工具版本**: FixMorph 