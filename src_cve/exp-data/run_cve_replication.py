#!/usr/bin/env python3
"""
run_cve_replication.py

该脚本作为 FixMorph CVE 数据集复现实验的一键入口。
它在不修改 FixMorph 核心代码的前提下，直接调用官方提供的
`experiments/ISSTA21/driver.py`，并将 `data/cve-data/cve-data.json`
作为实验元数据传递给 driver。

使用方式：
    python3 run_cve_replication.py [其它 driver 支持的参数]

☑ 默认行为
   - 自动推断 FixMorph 项目根目录以及 driver 脚本位置；
   - 默认使用 <项目根>/data/cve-data/cve-data.json 作为 --data 参数；
   - 保留并透传所有未知的命令行参数给 driver，使其与官方使用方式保持一致；
   - 如果想要指定其它数据集，可通过 --data=PATH 覆盖默认设置。

🚧 注意事项
   - driver 会克隆 Linux kernel 仓库（约 2GB+），确保磁盘空间与网络环境可用；
   - 实验过程会大量编译内核源码，对机器配置有一定要求；
   - 如需仅生成实验目录，可添加 `--only-setup`；如需跳过 setup，则可添加 `--skip-setup`。

作者: AI 助手
"""

import os
import sys
import subprocess
from typing import List


def get_project_root() -> str:
    """返回 FixMorph 项目根目录绝对路径。"""
    return os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir, os.pardir))


def build_driver_command(extra_args: List[str]) -> List[str]:
    """构造调用 driver.py 的命令行列表。

    参数
    ----
    extra_args: List[str]
        除 data 参数外，用户在执行脚本时提供的其余参数，这些参数将原样传递给 driver。

    返回
    ----
    List[str]
        最终可供 `subprocess.run` 直接执行的命令行列表。"""
    project_root = get_project_root()
    driver_path = os.path.join(project_root, "experiments", "ISSTA21", "driver.py")
    data_arg_provided = any(arg.startswith("--data=") for arg in extra_args)

    # 如果用户未显式指定 --data，使用默认数据集路径
    cmd = [sys.executable, driver_path]
    if not data_arg_provided:
        default_data_path = os.path.join(project_root, "data", "cve-data", "cve-data.json")
        cmd.append(f"--data={default_data_path}")
    cmd.extend(extra_args)  # 透传其它参数
    return cmd


def main():
    """脚本主入口。"""
    # sys.argv[1:] 为用户自定义的其它参数
    driver_cmd = build_driver_command(sys.argv[1:])

    print("[CVE-DRIVER] 即将执行 driver 脚本:")
    print(" \\n    ".join(driver_cmd))

    # 使用 run 而不是 check_call，方便后续处理返回码
    completed = subprocess.run(driver_cmd)
    if completed.returncode != 0:
        print("[CVE-DRIVER] driver 执行失败，返回码:", completed.returncode)
        sys.exit(completed.returncode)


if __name__ == "__main__":
    main() 