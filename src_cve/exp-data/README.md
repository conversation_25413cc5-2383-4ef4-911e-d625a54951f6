# CVE 数据集复现实验方案

> 本文档用于记录在 FixMorph 项目中基于 `data/cve-data/cve-data.json` 复现 Linux CVE 修复补丁实验的整体方案，以及对代码库所做的修改。

## 一、方案概述
1. **保持核心代码零侵入**：
   复现实验完全复用 FixMorph 官方在 `experiments/ISSTA21/driver.py` 中提供的逻辑，不修改任何现有实现。
2. **新增轻量级入口脚本**：
   在 `src/exp-data/run_cve_replication.py` 中封装对 driver 的调用，自动注入 CVE 数据集路径，屏蔽复杂命令行细节。
3. **可配置、可扩展**：
   • 默认使用 `data/cve-data/cve-data.json`，若用户希望使用其它数据集，只需在命令行追加 `--data=PATH` 覆盖。
   • 其余 driver 支持的参数（如 `--bug-id`、`--only-setup`、`--skip-setup` 等）均可透传。
4. **兼容官方 workflow**：
   生成的实验目录 (`/data/backport/linux-cve/...`) 与日志、输出结构保持一致，方便复用官方分析脚本。

### 运行示例
```bash
# 运行全部 CVE 实验
python3 src/exp-data/run_cve_replication.py

# 仅复现编号为 1 的 CVE
python3 src/exp-data/run_cve_replication.py --bug-id=1

# 只执行环境搭建，不立即运行 FixMorph
python3 src/exp-data/run_cve_replication.py --only-setup
```

## 二、目录与文件说明
| 路径 | 说明 |
|------|------|
| `src/exp-data/__init__.py` | 声明 `exp-data` 为 Python 包，当前无业务逻辑。 |
| `src/exp-data/run_cve_replication.py` | 复现实验入口脚本，构造并执行对 `experiments/ISSTA21/driver.py` 的调用。 |
| `src/exp-data/README.md` | 本文档，用于阐述方案与记录变更。 |

## 三、变更记录（Changelog）
| 日期 | 作者 | 描述 |
|------|------|------|
| 2025-06-11 | AI 助手 | • 新增 `src/exp-data/run_cve_replication.py`：实现一键复现脚本  <br/>• 新增 `src/exp-data/__init__.py`：声明包结构  <br/>• 新增 `src/exp-data/README.md`：方案说明与修改记录  <br/>• 新增 `src/exp-data/EXPERIMENT_REPORT.md`：CVE-1 复现实验完整报告 | 