#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复FixMorph的git历史问题

这个脚本会：
1. 清理现有的错误实验目录
2. 使用正确的git clone方式重新设置实验
3. 确保每个目录都有完整的git历史
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, 
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return result.returncode, result.stdout.decode(), result.stderr.decode()
    except Exception as e:
        return 1, "", str(e)

def clone_and_checkout(repo_url, commit_hash, target_dir):
    """克隆仓库并checkout到指定commit"""
    try:
        # 清理目标目录
        if target_dir.exists():
            shutil.rmtree(str(target_dir))

        # 确保父目录存在
        target_dir.parent.mkdir(parents=True, exist_ok=True)

        # 克隆仓库
        print("  🔄 Cloning repository...")
        ret_code, stdout, stderr = run_command("git clone {} {}".format(repo_url, str(target_dir)))

        if ret_code != 0:
            print("  ❌ Clone failed: {}".format(stderr))
            return False

        # 切换到指定commit
        print("  🔄 Checking out commit {}...".format(commit_hash[:8]))
        ret_code, stdout, stderr = run_command("git checkout {}".format(commit_hash), cwd=str(target_dir))
        
        if ret_code != 0:
            print("  ❌ Checkout failed: {}".format(stderr))
            return False
        
        # 验证git历史
        ret_code, stdout, stderr = run_command("git log --oneline -5", cwd=str(target_dir))
        if ret_code == 0:
            print("  ✅ Git history available ({} commits shown)".format(len(stdout.strip().split('\n'))))
        else:
            print("  ⚠️ Warning: Could not verify git history")
        
        return True
        
    except Exception as e:
        print("  ❌ Error: {}".format(e))
        return False

def main():
    print("🔧 FixMorph Git History Fix Tool")
    print("=" * 50)
    
    # CVE-2018-1118的配置
    cve_config = {
        'repo_url': 'https://github.com/torvalds/linux.git',
        'versions': {
            'pa': '55e49dc43a835b19567e62142cb1c87dc7db7b3c',  # 源版本漏洞commit
            'pb': '670ae9caaca467ea1bfd325cb2a5c98ba87f94ad',  # 源版本修复commit  
            'pc': 'ebeaa367548e9e92dd9374b9464ff6e7d157117b',  # 目标版本漏洞commit
            'pe': 'a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99',  # 目标版本修复commit
        }
    }
    
    # 实验目录
    experiment_dir = Path("experiments/test_sample/CVE-2018-1118")
    
    print("\n📋 Setting up CVE-2018-1118 with proper git history...")
    print("Repository: {}".format(cve_config['repo_url']))
    
    success_count = 0
    total_versions = len(cve_config['versions'])
    
    for version_name, commit_hash in cve_config['versions'].items():
        print("\n🔧 Setting up {}:".format(version_name))
        print("  Commit: {}".format(commit_hash))
        
        version_dir = experiment_dir / version_name
        
        if clone_and_checkout(cve_config['repo_url'], commit_hash, version_dir):
            success_count += 1
            print("  ✅ {} setup completed".format(version_name))
        else:
            print("  ❌ {} setup failed".format(version_name))
    
    print("\n📊 Summary:")
    print("  Successfully set up: {}/{}".format(success_count, total_versions))
    
    if success_count >= 3:
        print("  ✅ Sufficient versions for FixMorph experiment")
        
        # 验证所有目录都有git历史
        print("\n🔍 Verifying git history in all directories:")
        for version_name in cve_config['versions'].keys():
            version_dir = experiment_dir / version_name
            if version_dir.exists():
                ret_code, stdout, stderr = run_command("git log --oneline -1", cwd=str(version_dir))
                if ret_code == 0:
                    commit_info = stdout.strip()
                    print("  {}: ✅ {}".format(version_name, commit_info))
                else:
                    print("  {}: ❌ No git history".format(version_name))
        
        print("\n💡 Now you can run FixMorph:")
        print("   cd experiments/test_sample/CVE-2018-1118")
        print("   python3 /FixMorph/FixMorph.py --conf=repair.conf")
        
    else:
        print("  ❌ Insufficient versions for FixMorph experiment")
        print("  💡 You may need to check network connectivity or repository access")

if __name__ == "__main__":
    main()
