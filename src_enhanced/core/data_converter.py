#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FixMorph Enhanced Dataset Converter

将 enhanced_and_nvd_dataset.json 数据转换为 FixMorph 可处理的格式
"""

import json
import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))


class CVEDataConverter:
    """CVE数据转换器"""
    
    def __init__(self, input_file: str, output_dir: str = None):
        """
        初始化转换器
        
        Args:
            input_file: 输入的JSON文件路径
            output_dir: 输出目录，默认为 /FixMorph/experiments/
        """
        self.input_file = input_file
        self.output_dir = output_dir or "/FixMorph/experiments/enhanced_dataset"
        self.logger = self._setup_logger()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('CVEDataConverter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def load_data(self) -> List[Dict]:
        """加载CVE数据（列表格式）"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.logger.info("Successfully loaded {} CVEs from {}".format(len(data), self.input_file))
            return data
        except Exception as e:
            self.logger.error("Failed to load data: {}".format(e))
            raise
    
    def extract_commit_info(self, cve_data: Dict) -> Tuple[str, str, str]:
        """
        从CVE数据中提取commit信息
        
        Args:
            cve_data: CVE数据
            
        Returns:
            (vulnerable_commit, fixed_commit, repo_url)
        """
        try:
            # 提取版本信息
            versions = cve_data.get('versions', {})
            source_versions = versions.get('source', {})
            
            # 获取漏洞和修复的commit
            vulnerable_commit = source_versions.get('vulnerable', {}).get('commit', '')
            fixed_commit = source_versions.get('patched', {}).get('commit', '')
            
            # 获取仓库URL
            projects = cve_data.get('projects', {})
            repo_url = projects.get('source', {}).get('repo', '')
            
            return vulnerable_commit, fixed_commit, repo_url
            
        except Exception as e:
            self.logger.error("Failed to extract commit info: {}".format(e))
            return '', '', ''
    
    def convert_cve_to_fixmorph(self, cve_data: Dict) -> Dict:
        """
        将单个CVE数据转换为FixMorph格式
        
        Args:
            cve_data: CVE数据
            
        Returns:
            转换后的FixMorph格式数据
        """
        cve_id = cve_data.get('cve_id', 'unknown')
        
        # 提取项目信息
        projects = cve_data.get('projects', {})
        source_project = projects.get('source', {})
        project_name = source_project.get('name', 'unknown')
        
        # 提取仓库URL
        repo_url = source_project.get('repo', '')
        
        # 提取版本信息
        versions = cve_data.get('versions', {})
        source_versions = versions.get('source', {})
        target_versions = versions.get('target', {})
        
        # 提取commit信息
        source_vulnerable = source_versions.get('vulnerable', {}).get('commit', '')
        source_fixed = source_versions.get('patched', {}).get('commit', '')
        target_vulnerable = target_versions.get('vulnerable', {}).get('commit', '')
        target_fixed = target_versions.get('patched', {}).get('commit', '')
        
        # 从metadata获取手工移植的commit（如果有）
        metadata = cve_data.get('metadata', {})
        manual_port_commit = metadata.get('manual_port_commit', target_fixed)
        
        # 创建FixMorph配置（匹配GitManager期望的字段名）
        fixmorph_config = {
            'cve_id': cve_id,
            'project_name': project_name,
            'source_repo': repo_url,  # 修正：GitManager期望的字段名
            
            # GitManager期望的commit字段
            'pa': source_vulnerable,    # 源版本漏洞commit
            'pb': source_fixed,         # 源版本修复commit  
            'pc': target_vulnerable,    # 目标版本漏洞commit
            'pe': manual_port_commit,   # 目标版本修复commit (手工移植)
            
            # 额外的commit信息（向后兼容）
            'source_commit_a': source_vulnerable,
            'source_commit_b': source_fixed,
            'target_commit_a': target_vulnerable,
            'target_commit_b': manual_port_commit,
            
            # 从CVE数据提取changed_files（用于智能diff）
            'changed_files': self._extract_changed_files(cve_data),
            
            # 其他信息
            'language': source_project.get('language', 'C'),
            'patch_type': cve_data.get('patch_type', 'unknown'),
            'metadata': metadata
        }
        
        return fixmorph_config
    
    def _extract_changed_files(self, cve_data: Dict) -> List[str]:
        """从CVE数据中提取变更文件列表（用于智能diff）"""
        try:
            # 从metadata中获取affected_module信息
            metadata = cve_data.get('metadata', {})
            affected_module = metadata.get('affected_module_original', '')
            
            if affected_module:
                # 将.o文件转换为.c文件
                if affected_module.endswith('.o'):
                    c_file = affected_module.replace('.o', '.c')
                    return [c_file]
            
            # 如果没有明确的affected_module，尝试从CVE ID推断
            # 这里可以添加更多启发式规则
            cve_id = cve_data.get('cve_id', '')
            if 'vhost' in str(cve_data).lower():
                return ['drivers/vhost/vhost.c']
            
            return []
            
        except Exception as e:
            self.logger.warning(f"Failed to extract changed files: {e}")
            return []
    
    def generate_fixmorph_config(self, cve_data: Dict, experiment_dir: Path) -> Optional[Path]:
        """
        生成FixMorph所需的repair.conf文件
        
        Args:
            cve_data: 转换后的CVE数据
            experiment_dir: 实验目录
            
        Returns:
            配置文件路径
        """
        try:
            config_file = experiment_dir / "repair.conf"
            experiment_dir.mkdir(parents=True, exist_ok=True)
            
            conf_content = """tag: {}
project: {}
commit_a: {}
commit_b: {}
path_a: {}/pa
path_b: {}/pb
path_c: {}/pc
path_e: {}/pe
binary_name: {}
linux-kernel: false
version-control: none
""".format(
                cve_data['cve_id'],
                cve_data['project_name'],
                cve_data['commit_a'],
                cve_data['commit_b'],
                experiment_dir,
                experiment_dir,
                experiment_dir,
                experiment_dir,
                cve_data['project_name'].lower().replace(' ', '_')
            )
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(conf_content)
                
            self.logger.info("Generated repair.conf for {} at {}".format(cve_data['cve_id'], config_file))
            return config_file
            
        except Exception as e:
            self.logger.error("Failed to generate repair.conf for {}: {}".format(cve_data['cve_id'], e))
            return None
    
    def convert_single_cve(self, cve_data: Dict) -> Optional[Dict]:
        """
        转换单个CVE
        
        Args:
            cve_data: CVE原始数据
            
        Returns:
            转换后的数据，失败返回None
        """
        try:
            cve_id = cve_data.get('cve_id', 'unknown')
            
            # 转换数据
            fixmorph_data = self.convert_cve_to_fixmorph(cve_data)
            
            # 验证必要字段
            if not fixmorph_data['commit_a'] or not fixmorph_data['commit_b']:
                self.logger.warning("Missing commit info for {}, skipping".format(cve_id))
                return None
            
            self.logger.info("Successfully converted {}".format(cve_id))
            return fixmorph_data
                
        except Exception as e:
            self.logger.error("Failed to convert {}: {}".format(cve_data.get('cve_id', 'unknown'), e))
            return None
    
    def convert_all(self, max_count: int = None) -> Tuple[List[Dict], List[str]]:
        """
        转换所有CVE（修复方法名）
        
        Args:
            max_count: 最大转换数量，None表示全部
            
        Returns:
            (成功转换的数据列表, 失败的CVE ID列表)
        """
        data = self.load_data()
        
        if max_count:
            data = data[:max_count]
        
        converted_data = []
        failed_cves = []
        
        for cve_raw_data in data:
            cve_id = cve_raw_data.get('cve_id', 'unknown')
            converted = self.convert_single_cve(cve_raw_data)
            
            if converted:
                converted_data.append(converted)
            else:
                failed_cves.append(cve_id)
        
        self.logger.info("Conversion completed: {}/{} successful".format(len(converted_data), len(data)))
        return converted_data, failed_cves
    
    def save_converted_data(self, converted_data: List[Dict]) -> str:
        """保存转换后的数据"""
        os.makedirs(self.output_dir, exist_ok=True)
        output_file = os.path.join(self.output_dir, "converted_fixmorph_data.json")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info("Saved converted data to {}".format(output_file))
        return output_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Convert CVE data to FixMorph format')
    parser.add_argument('--input', '-i', required=True, help='Input JSON file path')
    parser.add_argument('--output', '-o', default='/FixMorph/experiments/', help='Output directory')
    parser.add_argument('--cve', '-c', help='Specific CVE ID to convert')
    parser.add_argument('--list', '-l', action='store_true', help='List all available CVEs')
    parser.add_argument('--all', '-a', action='store_true', help='Convert all CVEs')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger('CVEDataConverter').setLevel(logging.DEBUG)
    
    # 创建转换器
    converter = CVEDataConverter(args.input, args.output)
    
    # 执行操作
    if args.list:
        # The original code had a list_available_cves method, but it's not directly
        # called here. The new code loads data and iterates.
        # For now, we'll just print the number of CVEs loaded.
        data = converter.load_data()
        print(f"Available CVEs ({len(data)}):")
        for cve in sorted(data):
            print(f"  - {cve.get('cve_id', 'unknown')}")
    elif args.cve:
        # The original code had a convert_single_cve method, but it expects a cve_id.
        # The new code expects a cve_data dictionary.
        # We'll try to find the cve_data by cve_id.
        data = converter.load_data()
        cve_to_convert = None
        for cve_item in data:
            if cve_item.get('cve_id') == args.cve:
                cve_to_convert = cve_item
                break
        
        if cve_to_convert:
            converted = converter.convert_single_cve(cve_to_convert)
            if converted:
                print(f"Successfully converted {args.cve}")
            else:
                print(f"Failed to convert {args.cve}")
                sys.exit(1)
        else:
            print(f"CVE {args.cve} not found in input data.")
            sys.exit(1)
    elif args.all:
        converted_data, failed_cves = converter.convert_all()
        print(f"Conversion completed: {len(converted_data)}/{len(converter.load_data())} successful")
        
        if failed_cves:
            print("Failed CVEs:")
            for cve_id in failed_cves:
                print(f"  - {cve_id}")
    else:
        parser.print_help()


if __name__ == '__main__':
    main() 