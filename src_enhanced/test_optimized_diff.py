#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化diff功能测试脚本

快速测试和验证我们实现的针对CVE影响文件的优化diff功能
"""

import os
import sys
import json
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src_enhanced.core.optimized_differ import OptimizedDiffer, create_optimized_differ
from src_enhanced.core.enhanced_fixmorph_integration import EnhancedFixMorphIntegration


def setup_test_logger():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger('OptimizedDiffTest')


def test_optimized_differ_basic():
    """测试OptimizedDiffer基本功能"""
    logger = setup_test_logger()
    logger.info("=== 测试OptimizedDiffer基本功能 ===")
    
    # 创建测试CVE数据
    test_cve_data = {
        'cve_id': 'CVE-2018-1118',
        'changed_files': ['crypto/sha256_glue.c'],
        'commit_info': {
            'vulnerable_commit': 'abc123',
            'fixed_commit': 'def456'
        }
    }
    
    try:
        # 创建differ实例
        differ = create_optimized_differ(
            cve_data=test_cve_data,
            max_workers=2,
            enable_parallel=True
        )
        
        logger.info("✅ OptimizedDiffer实例创建成功")
        
        # 测试CVE文件提取功能
        project_a = "/FixMorph/experiments/test_sample/CVE-2018-1118/pa"
        project_b = "/FixMorph/experiments/test_sample/CVE-2018-1118/pb"
        
        if os.path.exists(project_a) and os.path.exists(project_b):
            affected_files = differ.extract_cve_affected_files(project_a, project_b)
            logger.info("✅ 提取到CVE影响文件: {}".format(affected_files))

            # 统计项目文件数
            total_files = differ.count_total_project_files(project_a)
            logger.info("✅ 项目总文件数: {}".format(total_files))

            if len(affected_files) > 0 and total_files > len(affected_files):
                optimization_ratio = len(affected_files) / float(total_files)
                logger.info("🎯 优化比例: {:.1f}% (处理{}/{}个文件)".format((1-optimization_ratio)*100, len(affected_files), total_files))
            
        else:
            logger.warning("⚠️ 测试项目路径不存在，跳过文件提取测试")
        
        return True
        
    except Exception as e:
        logger.error("❌ OptimizedDiffer基本功能测试失败: {}".format(e))
        return False


def test_enhanced_integration():
    """测试增强集成功能"""
    logger = setup_test_logger()
    logger.info("=== 测试增强集成功能 ===")
    
    try:
        # 创建集成器实例
        integration = EnhancedFixMorphIntegration(
            enable_optimization=True,
            performance_comparison=False  # 为了快速测试，暂时关闭性能对比
        )
        
        logger.info("✅ EnhancedFixMorphIntegration实例创建成功")
        
        # 测试repair.conf解析
        repair_conf_path = "/FixMorph/experiments/test_sample/CVE-2018-1118/repair.conf"
        if os.path.exists(repair_conf_path):
            cve_info = integration.extract_cve_from_repair_conf(repair_conf_path)
            logger.info("✅ repair.conf解析成功: {}".format(cve_info.get('cve_id', 'unknown')))
        else:
            logger.warning("⚠️ repair.conf文件不存在，跳过解析测试")
        
        return True
        
    except Exception as e:
        logger.error("❌ 增强集成功能测试失败: {}".format(e))
        return False


def test_performance_comparison():
    """测试性能对比功能（简化版）"""
    logger = setup_test_logger()
    logger.info("=== 测试性能对比功能 ===")
    
    try:
        # 模拟性能对比数据
        optimized_result = {
            'success': True,
            'processing_time': 2.5,
            'summary': {
                'total_files_in_project': 50000,
                'files_processed': 1,
                'optimization_ratio': 0.00002
            }
        }
        
        original_result = {
            'success': True,
            'processing_time': 120.0,
            'method': 'original_full_project_diff'
        }
        
        # 计算性能改进
        time_improvement = ((original_result['processing_time'] - optimized_result['processing_time']) 
                           / original_result['processing_time']) * 100
        
        processing_reduction = (1 - optimized_result['summary']['optimization_ratio']) * 100
        
        logger.info("✅ 模拟性能对比完成:")
        logger.info("  - 时间改进: {:.1f}%".format(time_improvement))
        logger.info("  - 处理量减少: {:.1f}%".format(processing_reduction))
        logger.info("  - 优化文件数: {}".format(optimized_result['summary']['total_files_in_project'] - optimized_result['summary']['files_processed']))
        
        return True
        
    except Exception as e:
        logger.error("❌ 性能对比功能测试失败: {}".format(e))
        return False


def test_file_operations():
    """测试文件操作功能"""
    logger = setup_test_logger()
    logger.info("=== 测试文件操作功能 ===")
    
    try:
        # 测试输出目录创建
        test_output_dir = "/tmp/test_optimized_diff"
        os.makedirs(test_output_dir, exist_ok=True)
        
        # 创建测试文件
        test_files = {
            'optimized_diff_C': 'Files /path/a/test.c and /path/b/test.c differ\n',
            'optimized_diff_H': 'Files /path/a/test.h and /path/b/test.h differ\n',
            'optimized_diff_results.json': json.dumps({
                'timestamp': 1234567890,
                'optimization_stats': {
                    'total_files_in_project': 1000,
                    'cve_affected_files': 2,
                    'files_processed': 2
                }
            }, indent=2)
        }
        
        for filename, content in test_files.items():
            file_path = os.path.join(test_output_dir, filename)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ 创建测试文件: {filename}")
        
        # 清理测试文件
        import shutil
        shutil.rmtree(test_output_dir)
        logger.info("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        logger.error("❌ 文件操作功能测试失败: {}".format(e))
        return False


def run_all_tests():
    """运行所有测试"""
    logger = setup_test_logger()
    logger.info("🚀 开始运行优化diff功能测试套件")
    
    tests = [
        ("OptimizedDiffer基本功能", test_optimized_differ_basic),
        ("增强集成功能", test_enhanced_integration),
        ("性能对比功能", test_performance_comparison),
        ("文件操作功能", test_file_operations)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info("\n--- 运行测试: {} ---".format(test_name))
        try:
            if test_func():
                logger.info("✅ {} - 通过".format(test_name))
                passed += 1
            else:
                logger.error("❌ {} - 失败".format(test_name))
                failed += 1
        except Exception as e:
            logger.error("❌ {} - 异常: {}".format(test_name, e))
            failed += 1
    
    # 测试总结
    total = passed + failed
    logger.info(f"\n=== 测试总结 ===")
    logger.info("总测试数: {}".format(total))
    logger.info("通过: {}".format(passed))
    logger.info("失败: {}".format(failed))
    logger.info("成功率: {:.1f}%".format((passed/float(total))*100))
    
    if failed == 0:
        logger.info("🎉 所有测试通过！优化diff功能工作正常")
        return True
    else:
        logger.warning(f"⚠️ 有{failed}个测试失败，需要检查相关功能")
        return False


def main():
    """主函数"""
    logger = setup_test_logger()
    
    print("优化diff功能测试脚本")
    print("=" * 50)
    
    try:
        success = run_all_tests()
        if success:
            print("\n✅ 测试完成 - 所有功能正常")
            sys.exit(0)
        else:
            print("\n❌ 测试完成 - 部分功能存在问题")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 