#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FixMorph Enhanced Dataset 使用示例

演示如何使用转换器和实验运行器
"""

import sys
import json
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src_enhanced.core.data_converter import CVEDataConverter
from src_enhanced.experiment_runner import ExperimentRunner


def example_convert_data():
    """示例：转换数据格式"""
    print("🔄 Step 1: Converting data format...")
    
    # 输入文件路径
    input_file = "/FixMorph/data/enhanced_data/enhanced_and_nvd_dataset.json"
    output_dir = "/FixMorph/experiments/enhanced_dataset"
    
    # 创建转换器
    converter = CVEDataConverter(input_file, output_dir)
    
    # 执行转换（最多转换20个CVE进行测试）
    converted_data, failed_cves = converter.convert_all(max_count=20)
    
    # 保存转换后的数据
    output_file = converter.save_converted_data(converted_data)
    
    print("✅ Conversion completed!")
    print("   📊 Successfully converted: {} CVEs".format(len(converted_data)))
    print("   ❌ Failed: {} CVEs".format(len(failed_cves)))
    print("   📁 Output file: {}".format(output_file))
    
    return output_file


def example_run_single_experiment():
    """示例：运行单个实验"""
    print("\n🧪 Step 2: Running single experiment...")
    
    # 选择一个CVE进行测试
    test_cve = "CVE-2018-1118"
    
    runner = ExperimentRunner(
        data_file="/FixMorph/experiments/enhanced_dataset/converted_data.json",
        max_workers=1,
        timeout=1800  # 30分钟超时
    )
    
    # 运行单个CVE实验
    results = runner.run_batch_experiments(
        cve_list=[test_cve],
        keep_results=True
    )
    
    # 保存结果
    results_file = runner.save_results()
    runner.print_summary()
    
    print("📁 Single experiment results: {}".format(results_file))
    
    return results


def example_run_batch_experiments():
    """示例：批量运行实验"""
    print("\n🚀 Step 3: Running batch experiments...")
    
    runner = ExperimentRunner(
        data_file="/FixMorph/experiments/enhanced_dataset/converted_data.json",
        max_workers=2,
        timeout=3600  # 1小时超时
    )
    
    # 运行前10个CVE作为示例
    results = runner.run_batch_experiments(
        start_index=0,
        end_index=10,
        keep_results=False  # 不保留结果以节省空间
    )
    
    # 保存结果
    results_file = runner.save_results()
    runner.print_summary()
    
    print("📁 Batch experiment results: {}".format(results_file))
    
    return results


def main():
    """主函数：完整的使用示例"""
    print("🎯 FixMorph Enhanced Dataset Example")
    print("=" * 50)
    
    try:
        # 步骤1：转换数据格式
        converted_file = example_convert_data()
        
        # 步骤2：运行单个实验（可选）
        # single_results = example_run_single_experiment()
        
        # 步骤3：批量运行实验
        batch_results = example_run_batch_experiments()
        
        print("\n🎉 All examples completed successfully!")
        print("\n📝 Next steps:")
        print("1. Check the results in /FixMorph/experiments/enhanced_dataset/results/")
        print("2. Analyze the success/failure patterns")
        print("3. Adjust parameters and run more experiments")
        print("4. Use the experiment_runner.py directly for production runs")
        
    except Exception as e:
        print("❌ Example failed: {}".format(e))
        sys.exit(1)


if __name__ == "__main__":
    main()
