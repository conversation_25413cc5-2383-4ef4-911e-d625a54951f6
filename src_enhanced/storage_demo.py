#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
存储管理演示

展示存储空间管理功能和优化策略
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from modules.storage_manager import StorageManager


def demo_storage_info():
    """演示存储信息获取"""
    print("📊 Storage Information Demo")
    print("=" * 50)
    
    storage_manager = StorageManager(
        base_dir="/FixMorph/experiments/enhanced_dataset",
        max_storage_gb=50  # 设置较小的限制用于演示
    )
    
    # 显示存储摘要
    storage_manager.print_storage_summary()
    
    return storage_manager


def demo_space_estimation():
    """演示空间估算"""
    print("\n🔍 Space Estimation Demo")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    test_repos = [
        "https://github.com/torvalds/linux",
        "https://github.com/git/git", 
        "https://github.com/curl/curl",
        "https://gitlab.com/some-project/example"
    ]
    
    for repo_url in test_repos:
        estimated_size = storage_manager.estimate_experiment_size(repo_url)
        formatted_size = storage_manager._format_bytes(estimated_size)
        print(f"📦 {repo_url}")
        print(f"   Estimated size: {formatted_size}")
        
        # 检查是否有足够空间
        has_space = storage_manager.check_space_available(estimated_size)
        status = "✅ Available" if has_space else "❌ Insufficient"
        print(f"   Space check: {status}")
        print()


def demo_cleanup_strategies():
    """演示清理策略"""
    print("\n🧹 Cleanup Strategies Demo")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    print("1. 清理旧实验 (超过24小时)")
    cleaned = storage_manager.cleanup_old_experiments(max_age_hours=24)
    print(f"   Cleaned {cleaned} old experiments")
    
    print("\n2. 检查当前存储状态")
    storage_info = storage_manager.get_storage_info()
    if 'error' not in storage_info:
        status = storage_info['status']
        usage = storage_info['project']['usage_ratio']
        print(f"   Status: {status}")
        print(f"   Usage: {usage:.1%}")
        
        if status in ['warning', 'critical']:
            print("\n3. 执行自动清理")
            cleanup_performed = storage_manager.auto_cleanup()
            if cleanup_performed:
                print("   ✅ Auto cleanup completed")
                storage_manager.print_storage_summary()
            else:
                print("   ℹ️ No cleanup needed")
    else:
        print(f"   ❌ Error getting storage info: {storage_info['error']}")


def demo_monitoring():
    """演示存储监控"""
    print("\n👁️ Storage Monitoring Demo")
    print("=" * 50)
    
    storage_manager = StorageManager()
    
    print("Starting storage monitoring (will run for 30 seconds)...")
    storage_manager.start_monitoring(check_interval=10)
    
    # 模拟一些活动
    for i in range(3):
        print(f"Monitoring cycle {i+1}/3...")
        time.sleep(10)
        
        # 显示当前状态
        storage_info = storage_manager.get_storage_info()
        if 'error' not in storage_info:
            status = storage_info['status']
            usage = storage_info['project']['usage_ratio']
            print(f"   Current status: {status} ({usage:.1%} used)")
    
    print("Stopping monitoring...")
    storage_manager.stop_monitoring()
    print("✅ Monitoring stopped")


def demo_advanced_features():
    """演示高级功能"""
    print("\n🚀 Advanced Features Demo")
    print("=" * 50)
    
    storage_manager = StorageManager(
        max_storage_gb=20,  # 较小的限制
        warning_threshold=0.7,  # 70%警告
        critical_threshold=0.85  # 85%严重
    )
    
    print("1. 自定义阈值设置")
    print(f"   Max storage: {storage_manager.max_storage_gb}GB")
    print(f"   Warning threshold: {storage_manager.warning_threshold:.0%}")
    print(f"   Critical threshold: {storage_manager.critical_threshold:.0%}")
    
    print("\n2. 目录大小计算")
    base_dir = Path("/FixMorph/experiments/enhanced_dataset")
    if base_dir.exists():
        for subdir in ["shared_repos", "active_experiments", "results"]:
            subdir_path = base_dir / subdir
            if subdir_path.exists():
                size = storage_manager.get_directory_size(subdir_path)
                formatted_size = storage_manager._format_bytes(size)
                print(f"   {subdir}: {formatted_size}")
    
    print("\n3. 智能清理建议")
    storage_info = storage_manager.get_storage_info()
    if 'error' not in storage_info:
        status = storage_info['status']
        
        if status == 'critical':
            print("   🚨 建议: 立即清理，释放至少30%空间")
        elif status == 'warning':
            print("   ⚠️ 建议: 清理旧实验，监控使用情况")
        else:
            print("   ✅ 建议: 当前状态良好，继续监控")


def main():
    """主演示函数"""
    print("🗄️ FixMorph Storage Management Demo")
    print("=" * 60)
    
    try:
        # 1. 基本存储信息
        storage_manager = demo_storage_info()
        
        # 2. 空间估算
        demo_space_estimation()
        
        # 3. 清理策略
        demo_cleanup_strategies()
        
        # 4. 监控功能 (可选，需要时间)
        # demo_monitoring()
        
        # 5. 高级功能
        demo_advanced_features()
        
        print("\n🎉 Storage Management Demo Completed!")
        print("\n📝 Key Benefits:")
        print("✅ Shared repository caching (99%+ space savings)")
        print("✅ Automatic cleanup of old experiments")
        print("✅ Real-time storage monitoring")
        print("✅ Intelligent space management")
        print("✅ Configurable thresholds and limits")
        
        print("\n🔧 Usage in Production:")
        print("1. Set appropriate storage limits based on your system")
        print("2. Enable monitoring for long-running experiments")
        print("3. Configure cleanup policies based on your needs")
        print("4. Monitor storage status regularly")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
