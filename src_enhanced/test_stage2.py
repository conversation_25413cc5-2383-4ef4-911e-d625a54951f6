#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
阶段2功能快速测试脚本
验证智能diff + 构建验证的集成效果
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src_enhanced.experiment_runner import ExperimentRunner


def create_test_data():
    """创建测试数据"""
    test_data = [{
        'cve_id': 'CVE-2018-1118-STAGE2-TEST',
        'source_repo': 'https://github.com/torvalds/linux.git',
        'source_commit_a': '55e49dc43a835b19567e62142cb7db7b3c',
        'source_commit_b': '670ae9caaca467ea1bfd325cb2a5c98ba87f94ad',
        'target_commit_a': 'a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99',
        'target_commit_b': '9681c3bdb098f6c87a0422b6b63912c1b90ad197',
        'changed_files': ['drivers/vhost/vhost.c']  # 已知的CVE变更文件
    }]
    
    # 保存到临时文件
    test_file = "/tmp/stage2_test_data.json"
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    return test_file, test_data[0]


def test_stage2_config_generation():
    """测试阶段2配置生成"""
    print("🧪 测试阶段2配置生成...")
    
    test_file, test_cve = create_test_data()
    
    # 创建ExperimentRunner实例（阶段2模式）
    runner = ExperimentRunner(
        data_file=test_file,
        output_dir="/tmp/stage2_test",
        stage2_mode=True,
        build_verification=True,
        timeout=1800
    )
    
    # 创建临时实验目录
    experiment_dir = Path("/tmp/stage2_test/CVE-2018-1118-STAGE2-TEST")
    experiment_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成阶段2配置
    config_file = runner.generate_stage2_config(test_cve, experiment_dir)
    
    # 验证配置文件
    print(f"✅ 配置文件生成: {config_file}")
    
    with open(config_file, 'r') as f:
        config_content = f.read()
    
    # 检查关键配置
    checks = [
        ('linux-kernel:true', '智能diff启用'),
        ('drivers/vhost/vhost.o', '智能构建目标'),
        ('cleanup_after_build:true', '资源管理'),
        ('timeout:1800', '超时设置')
    ]
    
    print("\n📋 配置验证:")
    for check, description in checks:
        if check in config_content:
            print(f"   ✅ {description}: {check}")
        else:
            print(f"   ❌ {description}: 缺失 {check}")
    
    print(f"\n📄 生成的配置文件内容:")
    print("=" * 50)
    print(config_content)
    print("=" * 50)
    
    return True


def test_stage2_vs_stage1():
    """对比阶段1和阶段2的配置差异"""
    print("\n🔍 对比阶段1 vs 阶段2配置...")
    
    test_file, test_cve = create_test_data()
    
    # 阶段1配置
    runner1 = ExperimentRunner(
        data_file=test_file,
        output_dir="/tmp/stage1_test",
        stage2_mode=False,
        build_verification=False
    )
    
    # 阶段2配置
    runner2 = ExperimentRunner(
        data_file=test_file,
        output_dir="/tmp/stage2_test",
        stage2_mode=True,
        build_verification=True
    )
    
    print("📊 配置对比:")
    print(f"   阶段1 - stage2_mode: {runner1.stage2_mode}")
    print(f"   阶段1 - build_verification: {runner1.build_verification}")
    print(f"   阶段2 - stage2_mode: {runner2.stage2_mode}")
    print(f"   阶段2 - build_verification: {runner2.build_verification}")
    
    return True


def demonstrate_performance_expectations():
    """演示性能预期"""
    print("\n📈 阶段2性能预期:")
    
    scenarios = [
        {
            'name': '原始FixMorph',
            'diff_time': '6小时',
            'build_time': '5分钟',
            'total_time': '>6小时',
            'quality': '✅'
        },
        {
            'name': '阶段1优化',
            'diff_time': '0.28秒',
            'build_time': '跳过',
            'total_time': '<1分钟',
            'quality': '⚠️'
        },
        {
            'name': '阶段2快速',
            'diff_time': '0.28秒',
            'build_time': '跳过',
            'total_time': '<1分钟',
            'quality': '✅'
        },
        {
            'name': '阶段2智能',
            'diff_time': '0.28秒',
            'build_time': '1-5分钟',
            'total_time': '<10分钟',
            'quality': '✅✅'
        }
    ]
    
    print(f"{'模式':<12} {'diff时间':<10} {'构建时间':<10} {'总时间':<10} {'质量'}")
    print("-" * 55)
    for scenario in scenarios:
        print(f"{scenario['name']:<12} {scenario['diff_time']:<10} {scenario['build_time']:<10} {scenario['total_time']:<10} {scenario['quality']}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 阶段2：完整验证功能测试")
    print("=" * 50)
    
    tests = [
        test_stage2_config_generation,
        test_stage2_vs_stage1,
        demonstrate_performance_expectations
    ]
    
    for test_func in tests:
        try:
            test_func()
            print()
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    print("🎉 阶段2功能测试完成!")
    print("\n🛠️  下一步建议:")
    print("1. 使用 --fast-diff-only 先验证智能diff效果")
    print("2. 使用 --stage2 --build-verification 启用完整验证")
    print("3. 根据需求选择合适的构建验证级别")
    
    print("\n📋 快速开始命令:")
    print("cd /FixMorph")
    print("python3.7 src_enhanced/experiment_runner.py --data your_data.json --fast-diff-only")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 