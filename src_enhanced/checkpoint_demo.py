#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
断点重连功能演示

展示如何使用断点重连系统进行实验恢复和状态管理
"""

import sys
import time
import signal
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from modules.checkpoint_manager import CheckpointManager, ExperimentStatus
from experiment_runner import ExperimentRunner


def demo_basic_checkpoint():
    """演示基本的断点功能"""
    print("📋 Basic Checkpoint Demo")
    print("=" * 50)
    
    checkpoint_manager = CheckpointManager()
    
    # 模拟一些实验状态
    test_cves = ["CVE-2018-1118", "CVE-2018-1119", "CVE-2019-3701"]
    
    print("1. 创建实验状态...")
    for i, cve_id in enumerate(test_cves):
        if i == 0:
            checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.SUCCESS)
        elif i == 1:
            checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.FAILED)
        else:
            checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.PENDING)
    
    print("2. 查看状态摘要...")
    checkpoint_manager.print_status_summary()
    
    print("\n3. 获取待执行的实验...")
    pending = checkpoint_manager.get_pending_experiments(test_cves)
    print(f"   Pending experiments: {pending}")
    
    print("\n4. 获取已完成的实验...")
    completed = checkpoint_manager.get_completed_experiments(test_cves)
    print(f"   Completed experiments: {completed}")
    
    return checkpoint_manager


def demo_retry_mechanism():
    """演示重试机制"""
    print("\n🔁 Retry Mechanism Demo")
    print("=" * 50)
    
    checkpoint_manager = CheckpointManager(max_retries=2, retry_delay=5)
    
    test_cve = "CVE-2018-RETRY-TEST"
    
    print("1. 模拟实验失败...")
    checkpoint_manager.update_experiment_status(test_cve, ExperimentStatus.FAILED, 
                                              {'error': 'Simulated failure'})
    
    status = checkpoint_manager.get_experiment_status(test_cve)
    print(f"   Status after failure: {status['status']}")
    print(f"   Retry count: {status['retry_count']}")
    
    print("\n2. 等待重试时间...")
    time.sleep(6)  # 等待重试延迟
    
    pending = checkpoint_manager.get_pending_experiments([test_cve])
    print(f"   Pending after retry delay: {pending}")
    
    print("\n3. 再次失败...")
    checkpoint_manager.update_experiment_status(test_cve, ExperimentStatus.FAILED,
                                              {'error': 'Second failure'})
    
    status = checkpoint_manager.get_experiment_status(test_cve)
    print(f"   Status after second failure: {status['status']}")
    print(f"   Retry count: {status['retry_count']}")
    
    print("\n4. 最终失败...")
    checkpoint_manager.update_experiment_status(test_cve, ExperimentStatus.FAILED,
                                              {'error': 'Final failure'})
    
    status = checkpoint_manager.get_experiment_status(test_cve)
    print(f"   Final status: {status['status']}")
    print(f"   Final retry count: {status['retry_count']}")
    
    failed_experiments = checkpoint_manager.get_failed_experiments([test_cve])
    print(f"   In failed list: {len(failed_experiments) > 0}")


def demo_checkpoint_save_load():
    """演示检查点保存和加载"""
    print("\n💾 Checkpoint Save/Load Demo")
    print("=" * 50)
    
    checkpoint_manager = CheckpointManager()
    
    # 创建一些测试状态
    test_data = {
        "CVE-2018-SAVE-1": ExperimentStatus.SUCCESS,
        "CVE-2018-SAVE-2": ExperimentStatus.FAILED,
        "CVE-2018-SAVE-3": ExperimentStatus.RUNNING,
    }
    
    print("1. 创建测试状态...")
    for cve_id, status in test_data.items():
        checkpoint_manager.update_experiment_status(cve_id, status)
    
    print("2. 保存检查点...")
    checkpoint_name = f"demo_{int(time.time())}"
    checkpoint_file = checkpoint_manager.save_checkpoint(
        checkpoint_name, 
        {'demo': True, 'timestamp': time.time()}
    )
    print(f"   Saved to: {checkpoint_file}")
    
    print("\n3. 修改状态...")
    checkpoint_manager.update_experiment_status("CVE-2018-SAVE-1", ExperimentStatus.FAILED)
    checkpoint_manager.update_experiment_status("CVE-2018-NEW", ExperimentStatus.PENDING)
    
    print("4. 当前状态:")
    checkpoint_manager.print_status_summary()
    
    print("\n5. 加载检查点...")
    success = checkpoint_manager.load_checkpoint(checkpoint_name)
    print(f"   Load success: {success}")
    
    print("6. 恢复后的状态:")
    checkpoint_manager.print_status_summary()


def demo_resume_experiments():
    """演示实验恢复"""
    print("\n🔄 Resume Experiments Demo")
    print("=" * 50)
    
    # 注意：这个演示不会实际运行FixMorph，只是展示恢复逻辑
    print("1. 模拟中断的实验批次...")
    
    checkpoint_manager = CheckpointManager()
    
    # 模拟一个被中断的实验批次
    test_cves = [f"CVE-2018-RESUME-{i}" for i in range(1, 6)]
    
    # 模拟部分完成的状态
    checkpoint_manager.update_experiment_status(test_cves[0], ExperimentStatus.SUCCESS)
    checkpoint_manager.update_experiment_status(test_cves[1], ExperimentStatus.SUCCESS)
    checkpoint_manager.update_experiment_status(test_cves[2], ExperimentStatus.FAILED)  # 会重试
    checkpoint_manager.update_experiment_status(test_cves[3], ExperimentStatus.RUNNING)  # 僵尸进程
    # test_cves[4] 没有状态，是新的
    
    print("2. 检查恢复状态...")
    pending = checkpoint_manager.get_pending_experiments(test_cves)
    completed = checkpoint_manager.get_completed_experiments(test_cves)
    
    print(f"   Total CVEs: {len(test_cves)}")
    print(f"   Completed: {len(completed)} - {completed}")
    print(f"   Pending: {len(pending)} - {pending}")
    
    print("\n3. 模拟恢复运行...")
    print(f"   Would resume with {len(pending)} experiments")
    print(f"   Skipping {len(completed)} already completed experiments")
    
    # 展示如何在实际代码中使用
    print("\n4. 实际使用示例:")
    print("   runner = ExperimentRunner(...)")
    print("   # 自动恢复模式（默认）")
    print("   runner.run_batch_experiments(cve_list=test_cves, resume=True)")
    print("   # 重新开始模式")
    print("   runner.run_batch_experiments(cve_list=test_cves, resume=False)")


def demo_monitoring_and_cleanup():
    """演示监控和清理功能"""
    print("\n🧹 Monitoring and Cleanup Demo")
    print("=" * 50)
    
    checkpoint_manager = CheckpointManager()
    
    print("1. 创建一些旧状态...")
    old_time = time.time() - (25 * 3600)  # 25小时前
    
    # 手动设置旧时间戳
    test_old_cves = ["CVE-2018-OLD-1", "CVE-2018-OLD-2"]
    for cve_id in test_old_cves:
        checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.SUCCESS)
        # 手动修改时间戳
        checkpoint_manager._state_cache[cve_id]['updated_at'] = old_time
    
    print("2. 创建一些新状态...")
    new_cves = ["CVE-2018-NEW-1", "CVE-2018-NEW-2"]
    for cve_id in new_cves:
        checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.PENDING)
    
    print("3. 清理前的状态:")
    checkpoint_manager.print_status_summary()
    
    print("\n4. 执行清理...")
    cleaned = checkpoint_manager.cleanup_old_states(max_age_days=1)
    print(f"   Cleaned {cleaned} old states")
    
    print("\n5. 清理后的状态:")
    checkpoint_manager.print_status_summary()
    
    print("\n6. 重置失败的实验...")
    # 创建一些失败的实验
    failed_cves = ["CVE-2018-FAILED-1", "CVE-2018-FAILED-2"]
    for cve_id in failed_cves:
        checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.FAILED)
        # 设置为最大重试次数
        checkpoint_manager._state_cache[cve_id]['retry_count'] = checkpoint_manager.max_retries
    
    reset_count = checkpoint_manager.reset_failed_experiments()
    print(f"   Reset {reset_count} failed experiments")


def simulate_interrupted_experiment():
    """模拟中断的实验"""
    print("\n⚡ Simulated Interrupted Experiment")
    print("=" * 50)
    
    def signal_handler(signum, frame):
        print("\n🛑 Experiment interrupted! Saving state...")
        checkpoint_manager.save_checkpoint("interrupted_experiment")
        print("✅ State saved. You can resume later with:")
        print("   python experiment_runner.py --resume")
        sys.exit(0)
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    checkpoint_manager = CheckpointManager()
    
    print("Starting simulated experiment...")
    print("Press Ctrl+C to simulate interruption")
    
    test_cves = [f"CVE-2018-SIM-{i}" for i in range(1, 11)]
    
    try:
        for i, cve_id in enumerate(test_cves):
            print(f"Processing {cve_id} ({i+1}/{len(test_cves)})...")
            
            # 模拟实验过程
            checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.SETUP)
            time.sleep(0.5)
            
            checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.RUNNING)
            time.sleep(1)
            
            # 随机成功或失败
            import random
            if random.random() > 0.3:
                checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.SUCCESS)
                print(f"   ✅ {cve_id} completed")
            else:
                checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.FAILED)
                print(f"   ❌ {cve_id} failed")
        
        print("\n🎉 All experiments completed without interruption!")
        
    except KeyboardInterrupt:
        # 这个不会被调用，因为我们设置了信号处理器
        pass


def main():
    """主演示函数"""
    print("🔄 FixMorph Checkpoint & Resume Demo")
    print("=" * 60)
    
    try:
        # 1. 基本断点功能
        checkpoint_manager = demo_basic_checkpoint()
        
        # 2. 重试机制
        demo_retry_mechanism()
        
        # 3. 检查点保存和加载
        demo_checkpoint_save_load()
        
        # 4. 实验恢复
        demo_resume_experiments()
        
        # 5. 监控和清理
        demo_monitoring_and_cleanup()
        
        # 6. 模拟中断实验（可选）
        print("\n" + "=" * 60)
        choice = input("Do you want to simulate an interrupted experiment? (y/N): ")
        if choice.lower() == 'y':
            simulate_interrupted_experiment()
        
        print("\n🎉 Checkpoint Demo Completed!")
        print("\n📝 Key Features:")
        print("✅ Automatic experiment state tracking")
        print("✅ Intelligent retry mechanism with backoff")
        print("✅ Checkpoint save/load for manual recovery")
        print("✅ Resume from interruption points")
        print("✅ Zombie process detection and recovery")
        print("✅ Automatic cleanup of old states")
        
        print("\n🔧 Usage in Production:")
        print("1. Run experiments with automatic resume: --resume (default)")
        print("2. Force restart all experiments: --resume=false")
        print("3. Save manual checkpoints before risky operations")
        print("4. Monitor experiment status with checkpoint_manager.print_status_summary()")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
