# FixMorph 阶段2配置模板 - 完整验证 (确保质量)
# 用于大规模CVE实验的生产级配置

# ==================== 基本路径配置 ====================
path_a:{PA_PATH}
path_b:{PB_PATH}  
path_c:{PC_PATH}
path_e:{PE_PATH}
tag_id:{CVE_ID}

# ==================== Git Commit信息 ====================
commit_a:{COMMIT_A}
commit_b:{COMMIT_B}
commit_c:{COMMIT_C}
commit_e:{COMMIT_E}

# ==================== 🚀 核心优化配置 ====================
# 关键：启用Linux内核智能diff，获得36,788倍性能提升
linux-kernel:true
backport:true
version-control:git

# ==================== 🔧 构建验证配置 ====================
# 阶段2：启用构建验证确保补丁质量
config_command_a:make allyesconfig
config_command_c:make allyesconfig

# 构建策略：分级验证
# 选项1：最小构建（推荐开始）- 验证语法和关键模块
build_command_a:make drivers/vhost/vhost.o
build_command_c:make drivers/vhost/vhost.o

# 选项2：模块构建（中等验证）- 验证整个子系统
# build_command_a:make drivers/vhost/
# build_command_c:make drivers/vhost/

# 选项3：全量构建（最高质量）- 完整验证但耗时很长
# build_command_a:make -j$(nproc) 
# build_command_c:make -j$(nproc)

# ==================== 🎯 实验优化设置 ====================
# 加速构建的优化选项
build_flags_a:-j$(nproc) --quiet
build_flags_c:-j$(nproc) --quiet

# 超时控制 - 防止单个实验卡死
timeout:1800  # 30分钟超时

# ==================== 📊 监控和调试 ====================
# 启用详细日志用于问题诊断
verbose:true
debug:false

# ==================== 💾 资源管理 ====================
# 大规模实验的资源控制
cleanup_after_build:true  # 构建后清理中间文件
keep_source:false         # 实验完成后是否保留源码
parallel_jobs:2           # 并行实验数量限制

# ==================== 🔍 验证级别控制 ====================
# 可选验证步骤（根据需求启用）
syntax_check:true         # 语法检查
semantic_check:false      # 语义分析（耗时）
runtime_test:false        # 运行时测试（耗时）

# ==================== 📝 注释说明 ====================
# 本配置针对Linux内核CVE实验优化：
# 1. 启用智能diff，避免处理30,880个无关文件
# 2. 分级构建验证，平衡质量和效率
# 3. 资源控制，支持大规模批量实验
# 4. 超时保护，防止实验卡死 