#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
存储空间管理模块

负责监控磁盘使用情况，管理存储空间，实现智能清理策略
"""

import os
import shutil
import psutil
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import threading


class StorageManager:
    """存储空间管理器"""
    
    def __init__(self, 
                 base_dir: str = "/FixMorph/experiments/enhanced_dataset",
                 max_storage_gb: int = 100,
                 warning_threshold: float = 0.8,
                 critical_threshold: float = 0.9):
        
        self.base_dir = Path(base_dir)
        self.max_storage_gb = max_storage_gb
        self.max_storage_bytes = max_storage_gb * 1024 * 1024 * 1024
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        
        self.logger = self._setup_logger()
        
        # 创建目录结构
        self.shared_repos_dir = self.base_dir / "shared_repos"
        self.active_experiments_dir = self.base_dir / "active_experiments"
        self.results_dir = self.base_dir / "results"
        
        for dir_path in [self.shared_repos_dir, self.active_experiments_dir, self.results_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 监控线程
        self._monitoring = False
        self._monitor_thread = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('StorageManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def get_directory_size(self, directory: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            self.logger.warning(f"Failed to calculate size for {directory}: {e}")
        return total_size
    
    def get_storage_info(self) -> Dict:
        """获取存储使用情况"""
        try:
            # 获取磁盘使用情况
            disk_usage = psutil.disk_usage(str(self.base_dir))
            
            # 获取各目录大小
            shared_repos_size = self.get_directory_size(self.shared_repos_dir)
            active_experiments_size = self.get_directory_size(self.active_experiments_dir)
            results_size = self.get_directory_size(self.results_dir)
            total_project_size = shared_repos_size + active_experiments_size + results_size
            
            # 计算使用率
            usage_ratio = total_project_size / self.max_storage_bytes
            disk_usage_ratio = (disk_usage.total - disk_usage.free) / disk_usage.total
            
            return {
                'disk': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'usage_ratio': disk_usage_ratio
                },
                'project': {
                    'shared_repos_size': shared_repos_size,
                    'active_experiments_size': active_experiments_size,
                    'results_size': results_size,
                    'total_size': total_project_size,
                    'max_allowed': self.max_storage_bytes,
                    'usage_ratio': usage_ratio
                },
                'status': self._get_status(usage_ratio, disk_usage_ratio)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get storage info: {e}")
            return {'error': str(e)}
    
    def _get_status(self, project_usage: float, disk_usage: float) -> str:
        """获取存储状态"""
        max_usage = max(project_usage, disk_usage)
        
        if max_usage >= self.critical_threshold:
            return 'critical'
        elif max_usage >= self.warning_threshold:
            return 'warning'
        else:
            return 'normal'
    
    def check_space_available(self, required_bytes: int) -> bool:
        """检查是否有足够的空间"""
        storage_info = self.get_storage_info()
        
        if 'error' in storage_info:
            return False
        
        # 检查项目限制
        current_size = storage_info['project']['total_size']
        if current_size + required_bytes > self.max_storage_bytes:
            return False
        
        # 检查磁盘空间
        free_space = storage_info['disk']['free']
        if required_bytes > free_space * 0.9:  # 保留10%缓冲
            return False
        
        return True
    
    def estimate_experiment_size(self, repo_url: str) -> int:
        """估算单个实验所需空间"""
        # 基于仓库类型估算
        if 'linux' in repo_url.lower() and 'kernel' in repo_url.lower():
            # Linux内核：4个版本 × 4GB
            return 4 * 4 * 1024 * 1024 * 1024  # 16GB
        elif 'github.com' in repo_url:
            # 一般GitHub项目：4个版本 × 500MB
            return 4 * 500 * 1024 * 1024  # 2GB
        else:
            # 其他项目：4个版本 × 1GB
            return 4 * 1024 * 1024 * 1024  # 4GB
    
    def cleanup_old_experiments(self, max_age_hours: int = 24) -> int:
        """清理旧的实验目录"""
        cleaned_count = 0
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        try:
            for experiment_dir in self.active_experiments_dir.iterdir():
                if experiment_dir.is_dir():
                    # 检查目录修改时间
                    dir_mtime = experiment_dir.stat().st_mtime
                    
                    if dir_mtime < cutoff_time:
                        self.logger.info(f"Cleaning up old experiment: {experiment_dir.name}")
                        shutil.rmtree(experiment_dir)
                        cleaned_count += 1
                        
        except Exception as e:
            self.logger.error(f"Failed to cleanup old experiments: {e}")
        
        return cleaned_count
    
    def cleanup_by_size(self, target_free_bytes: int) -> int:
        """按大小清理实验目录"""
        cleaned_count = 0
        
        try:
            # 获取所有实验目录及其大小
            experiments = []
            for experiment_dir in self.active_experiments_dir.iterdir():
                if experiment_dir.is_dir():
                    size = self.get_directory_size(experiment_dir)
                    mtime = experiment_dir.stat().st_mtime
                    experiments.append((experiment_dir, size, mtime))
            
            # 按修改时间排序（最旧的优先清理）
            experiments.sort(key=lambda x: x[2])
            
            freed_bytes = 0
            for experiment_dir, size, mtime in experiments:
                if freed_bytes >= target_free_bytes:
                    break
                
                self.logger.info(f"Cleaning up experiment for space: {experiment_dir.name}")
                shutil.rmtree(experiment_dir)
                freed_bytes += size
                cleaned_count += 1
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup by size: {e}")
        
        return cleaned_count
    
    def auto_cleanup(self) -> bool:
        """自动清理策略"""
        storage_info = self.get_storage_info()
        
        if 'error' in storage_info:
            return False
        
        status = storage_info['status']
        
        if status == 'critical':
            self.logger.warning("🚨 Critical storage usage! Performing aggressive cleanup...")
            
            # 清理所有超过1小时的实验
            cleaned = self.cleanup_old_experiments(max_age_hours=1)
            self.logger.info(f"Cleaned {cleaned} old experiments")
            
            # 如果还不够，按大小清理
            storage_info = self.get_storage_info()
            if storage_info['status'] == 'critical':
                target_free = self.max_storage_bytes * 0.3  # 释放到30%使用率
                current_used = storage_info['project']['total_size']
                need_to_free = current_used - target_free
                
                if need_to_free > 0:
                    cleaned += self.cleanup_by_size(need_to_free)
                    self.logger.info(f"Additional cleanup: {cleaned} experiments")
            
            return True
            
        elif status == 'warning':
            self.logger.warning("⚠️ High storage usage! Performing routine cleanup...")
            
            # 清理超过12小时的实验
            cleaned = self.cleanup_old_experiments(max_age_hours=12)
            self.logger.info(f"Cleaned {cleaned} old experiments")
            
            return True
        
        return False
    
    def start_monitoring(self, check_interval: int = 300):
        """启动存储监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(check_interval,),
            daemon=True
        )
        self._monitor_thread.start()
        self.logger.info(f"Storage monitoring started (check every {check_interval}s)")
    
    def stop_monitoring(self):
        """停止存储监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        self.logger.info("Storage monitoring stopped")
    
    def _monitor_loop(self, check_interval: int):
        """监控循环"""
        while self._monitoring:
            try:
                storage_info = self.get_storage_info()
                
                if 'error' not in storage_info:
                    status = storage_info['status']
                    
                    if status in ['warning', 'critical']:
                        self.auto_cleanup()
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(check_interval)
    
    def print_storage_summary(self):
        """打印存储使用摘要"""
        storage_info = self.get_storage_info()
        
        if 'error' in storage_info:
            print(f"❌ Storage info error: {storage_info['error']}")
            return
        
        project = storage_info['project']
        disk = storage_info['disk']
        status = storage_info['status']
        
        # 状态图标
        status_icon = {
            'normal': '✅',
            'warning': '⚠️',
            'critical': '🚨'
        }.get(status, '❓')
        
        print(f"\n{status_icon} Storage Summary")
        print("=" * 50)
        
        # 项目存储使用
        print(f"📁 Project Storage:")
        print(f"   Shared Repos: {self._format_bytes(project['shared_repos_size'])}")
        print(f"   Active Experiments: {self._format_bytes(project['active_experiments_size'])}")
        print(f"   Results: {self._format_bytes(project['results_size'])}")
        print(f"   Total: {self._format_bytes(project['total_size'])} / {self._format_bytes(project['max_allowed'])}")
        print(f"   Usage: {project['usage_ratio']:.1%}")
        
        # 磁盘使用
        print(f"\n💾 Disk Usage:")
        print(f"   Used: {self._format_bytes(disk['used'])} / {self._format_bytes(disk['total'])}")
        print(f"   Free: {self._format_bytes(disk['free'])}")
        print(f"   Usage: {disk['usage_ratio']:.1%}")
        
        # 状态和建议
        print(f"\n📊 Status: {status.upper()}")
        
        if status == 'critical':
            print("🚨 Action Required: Storage critically low!")
            print("   - Run cleanup immediately")
            print("   - Consider increasing storage limits")
        elif status == 'warning':
            print("⚠️  Warning: Storage usage high")
            print("   - Monitor closely")
            print("   - Consider cleanup")
    
    def _format_bytes(self, bytes_value: int) -> str:
        """格式化字节数为可读格式"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"
