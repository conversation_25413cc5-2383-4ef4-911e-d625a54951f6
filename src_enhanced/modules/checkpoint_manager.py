#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
断点重连管理模块

负责实验状态持久化、断点恢复、失败重试等功能
"""

import json
import os
import time
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Set
from enum import Enum
import fcntl


class ExperimentStatus(Enum):
    """实验状态枚举"""
    PENDING = "pending"           # 等待开始
    SETUP = "setup"              # 正在设置
    RUNNING = "running"          # 正在运行
    SUCCESS = "success"          # 成功完成
    FAILED = "failed"            # 失败
    TIMEOUT = "timeout"          # 超时
    SETUP_FAILED = "setup_failed" # 设置失败
    RETRY = "retry"              # 等待重试


class CheckpointManager:
    """断点重连管理器"""
    
    def __init__(self, 
                 checkpoint_dir: str = "/FixMorph/experiments/enhanced_dataset/checkpoints",
                 max_retries: int = 3,
                 retry_delay: int = 300):  # 5分钟重试延迟
        
        self.checkpoint_dir = Path(checkpoint_dir)
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        self.logger = self._setup_logger()
        
        # 状态文件路径
        self.state_file = self.checkpoint_dir / "experiment_state.json"
        self.lock_file = self.checkpoint_dir / "state.lock"
        
        # 内存中的状态缓存
        self._state_cache = {}
        self._lock = threading.RLock()
        
        # 加载现有状态
        self._load_state()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('CheckpointManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _load_state(self):
        """加载实验状态"""
        try:
            if self.state_file.exists():
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    self._state_cache = json.load(f)
                self.logger.info(f"Loaded {len(self._state_cache)} experiment states")
            else:
                self._state_cache = {}
                self.logger.info("No existing state file found, starting fresh")
        except Exception as e:
            self.logger.error(f"Failed to load state: {e}")
            self._state_cache = {}
    
    def _save_state(self):
        """保存实验状态到文件"""
        try:
            # 使用文件锁确保原子性写入
            with open(self.lock_file, 'w') as lock:
                fcntl.flock(lock.fileno(), fcntl.LOCK_EX)
                
                # 创建临时文件
                temp_file = self.state_file.with_suffix('.tmp')
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(self._state_cache, f, indent=2, ensure_ascii=False)
                
                # 原子性替换
                temp_file.replace(self.state_file)
                
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    def update_experiment_status(self, 
                                cve_id: str, 
                                status: ExperimentStatus,
                                details: Optional[Dict] = None):
        """更新实验状态"""
        with self._lock:
            current_time = time.time()
            
            if cve_id not in self._state_cache:
                self._state_cache[cve_id] = {
                    'cve_id': cve_id,
                    'status': status.value,
                    'created_at': current_time,
                    'updated_at': current_time,
                    'retry_count': 0,
                    'history': []
                }
            else:
                # 记录状态变化历史
                old_status = self._state_cache[cve_id]['status']
                self._state_cache[cve_id]['history'].append({
                    'from_status': old_status,
                    'to_status': status.value,
                    'timestamp': current_time
                })
                
                self._state_cache[cve_id]['status'] = status.value
                self._state_cache[cve_id]['updated_at'] = current_time
            
            # 更新详细信息
            if details:
                self._state_cache[cve_id].update(details)
            
            # 处理重试逻辑
            if status in [ExperimentStatus.FAILED, ExperimentStatus.TIMEOUT, ExperimentStatus.SETUP_FAILED]:
                retry_count = self._state_cache[cve_id]['retry_count']
                if retry_count < self.max_retries:
                    self._state_cache[cve_id]['retry_count'] = retry_count + 1
                    self._state_cache[cve_id]['next_retry_at'] = current_time + self.retry_delay
                    self._state_cache[cve_id]['status'] = ExperimentStatus.RETRY.value
                    self.logger.info(f"Scheduled retry {retry_count + 1}/{self.max_retries} for {cve_id}")
            
            # 保存到文件
            self._save_state()
            
            self.logger.info(f"Updated {cve_id}: {status.value}")
    
    def get_experiment_status(self, cve_id: str) -> Optional[Dict]:
        """获取实验状态"""
        with self._lock:
            return self._state_cache.get(cve_id)
    
    def get_experiments_by_status(self, status: ExperimentStatus) -> List[Dict]:
        """获取指定状态的实验"""
        with self._lock:
            return [
                exp for exp in self._state_cache.values()
                if exp['status'] == status.value
            ]
    
    def get_pending_experiments(self, cve_list: List[str]) -> List[str]:
        """获取需要执行的实验列表（包括重试）"""
        with self._lock:
            pending = []
            current_time = time.time()
            
            for cve_id in cve_list:
                exp_state = self._state_cache.get(cve_id)
                
                if not exp_state:
                    # 新实验
                    pending.append(cve_id)
                elif exp_state['status'] == ExperimentStatus.PENDING.value:
                    # 等待中的实验
                    pending.append(cve_id)
                elif exp_state['status'] == ExperimentStatus.RETRY.value:
                    # 检查是否到了重试时间
                    next_retry = exp_state.get('next_retry_at', 0)
                    if current_time >= next_retry:
                        pending.append(cve_id)
                        self.logger.info(f"Adding {cve_id} for retry")
                elif exp_state['status'] in [ExperimentStatus.SETUP.value, ExperimentStatus.RUNNING.value]:
                    # 检查是否是僵尸进程（运行时间过长）
                    updated_at = exp_state.get('updated_at', 0)
                    if current_time - updated_at > 7200:  # 2小时无更新认为是僵尸进程
                        self.logger.warning(f"Detected zombie experiment {cve_id}, adding for retry")
                        self.update_experiment_status(cve_id, ExperimentStatus.FAILED, 
                                                    {'error': 'Zombie process detected'})
                        pending.append(cve_id)
            
            return pending
    
    def get_completed_experiments(self, cve_list: List[str]) -> List[str]:
        """获取已完成的实验列表"""
        with self._lock:
            completed = []
            
            for cve_id in cve_list:
                exp_state = self._state_cache.get(cve_id)
                if exp_state and exp_state['status'] == ExperimentStatus.SUCCESS.value:
                    completed.append(cve_id)
            
            return completed
    
    def get_failed_experiments(self, cve_list: List[str]) -> List[Dict]:
        """获取最终失败的实验列表"""
        with self._lock:
            failed = []
            
            for cve_id in cve_list:
                exp_state = self._state_cache.get(cve_id)
                if exp_state:
                    status = exp_state['status']
                    retry_count = exp_state.get('retry_count', 0)
                    
                    # 失败且已达到最大重试次数
                    if (status in [ExperimentStatus.FAILED.value, 
                                  ExperimentStatus.TIMEOUT.value, 
                                  ExperimentStatus.SETUP_FAILED.value] and 
                        retry_count >= self.max_retries):
                        failed.append(exp_state)
            
            return failed
    
    def reset_experiment(self, cve_id: str):
        """重置实验状态"""
        with self._lock:
            if cve_id in self._state_cache:
                self._state_cache[cve_id]['status'] = ExperimentStatus.PENDING.value
                self._state_cache[cve_id]['retry_count'] = 0
                self._state_cache[cve_id]['updated_at'] = time.time()
                if 'next_retry_at' in self._state_cache[cve_id]:
                    del self._state_cache[cve_id]['next_retry_at']
                
                self._save_state()
                self.logger.info(f"Reset experiment {cve_id}")
    
    def reset_failed_experiments(self):
        """重置所有失败的实验"""
        with self._lock:
            reset_count = 0
            for cve_id, exp_state in self._state_cache.items():
                if exp_state['status'] in [ExperimentStatus.FAILED.value, 
                                          ExperimentStatus.TIMEOUT.value, 
                                          ExperimentStatus.SETUP_FAILED.value]:
                    self.reset_experiment(cve_id)
                    reset_count += 1
            
            self.logger.info(f"Reset {reset_count} failed experiments")
            return reset_count
    
    def cleanup_old_states(self, max_age_days: int = 30):
        """清理旧的实验状态"""
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - (max_age_days * 24 * 3600)
            
            to_remove = []
            for cve_id, exp_state in self._state_cache.items():
                if exp_state.get('updated_at', 0) < cutoff_time:
                    to_remove.append(cve_id)
            
            for cve_id in to_remove:
                del self._state_cache[cve_id]
            
            if to_remove:
                self._save_state()
                self.logger.info(f"Cleaned up {len(to_remove)} old experiment states")
            
            return len(to_remove)
    
    def export_summary(self) -> Dict:
        """导出实验状态摘要"""
        with self._lock:
            summary = {
                'total_experiments': len(self._state_cache),
                'status_counts': {},
                'retry_stats': {
                    'experiments_with_retries': 0,
                    'total_retries': 0,
                    'max_retries_reached': 0
                },
                'timestamp': time.time()
            }
            
            # 统计各状态数量
            for exp_state in self._state_cache.values():
                status = exp_state['status']
                summary['status_counts'][status] = summary['status_counts'].get(status, 0) + 1
                
                # 重试统计
                retry_count = exp_state.get('retry_count', 0)
                if retry_count > 0:
                    summary['retry_stats']['experiments_with_retries'] += 1
                    summary['retry_stats']['total_retries'] += retry_count
                    
                    if retry_count >= self.max_retries:
                        summary['retry_stats']['max_retries_reached'] += 1
            
            return summary
    
    def print_status_summary(self):
        """打印状态摘要"""
        summary = self.export_summary()
        
        print(f"\n📊 Experiment Status Summary")
        print("=" * 50)
        print(f"Total Experiments: {summary['total_experiments']}")
        
        print(f"\n📈 Status Distribution:")
        for status, count in summary['status_counts'].items():
            icon = {
                'success': '✅',
                'failed': '❌',
                'timeout': '⏰',
                'setup_failed': '🔧',
                'running': '🔄',
                'setup': '⚙️',
                'pending': '⏳',
                'retry': '🔁'
            }.get(status, '❓')
            
            percentage = (count / summary['total_experiments']) * 100
            print(f"   {icon} {status.upper()}: {count} ({percentage:.1f}%)")
        
        retry_stats = summary['retry_stats']
        if retry_stats['experiments_with_retries'] > 0:
            print(f"\n🔁 Retry Statistics:")
            print(f"   Experiments with retries: {retry_stats['experiments_with_retries']}")
            print(f"   Total retry attempts: {retry_stats['total_retries']}")
            print(f"   Max retries reached: {retry_stats['max_retries_reached']}")
    
    def save_checkpoint(self, checkpoint_name: str, additional_data: Dict = None):
        """保存检查点"""
        checkpoint_data = {
            'checkpoint_name': checkpoint_name,
            'timestamp': time.time(),
            'experiment_states': self._state_cache.copy(),
            'additional_data': additional_data or {}
        }
        
        checkpoint_file = self.checkpoint_dir / f"checkpoint_{checkpoint_name}.json"
        
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved checkpoint: {checkpoint_name}")
            return str(checkpoint_file)
            
        except Exception as e:
            self.logger.error(f"Failed to save checkpoint {checkpoint_name}: {e}")
            return None
    
    def load_checkpoint(self, checkpoint_name: str) -> bool:
        """加载检查点"""
        checkpoint_file = self.checkpoint_dir / f"checkpoint_{checkpoint_name}.json"
        
        if not checkpoint_file.exists():
            self.logger.error(f"Checkpoint not found: {checkpoint_name}")
            return False
        
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            with self._lock:
                self._state_cache = checkpoint_data['experiment_states']
                self._save_state()
            
            self.logger.info(f"Loaded checkpoint: {checkpoint_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to load checkpoint {checkpoint_name}: {e}")
            return False
