#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GitHub API 管理器 - 高效的仓库和commit管理
使用GitHub API替代git clone，实现按需下载和智能缓存
"""

import os
import requests
import json
import zipfile
import logging
from pathlib import Path
from typing import Dict, Optional, List
import hashlib
import time

class GitHubAPIManager:
    """GitHub API管理器 - 高效替代git clone"""
    
    def __init__(self, github_token: Optional[str] = None, cache_dir: str = "/FixMorph/experiments/enhanced_dataset/api_cache"):
        self.github_token = github_token
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = self._setup_logger()
        
        # API配置
        self.api_base = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'FixMorph-Enhanced/1.0'
        }
        
        if self.github_token:
            self.headers['Authorization'] = f'token {self.github_token}'
            self.logger.info("🔐 GitHub API authenticated access enabled")
        else:
            self.logger.warning("⚠️ Using GitHub API without authentication (60 requests/hour limit)")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('GitHubAPIManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _parse_github_url(self, repo_url: str) -> tuple:
        """解析GitHub仓库URL"""
        # https://github.com/torvalds/linux.git -> torvalds, linux
        if 'github.com' not in repo_url:
            raise ValueError(f"Not a GitHub repository: {repo_url}")
        
        # 清理URL
        repo_url = repo_url.replace('.git', '')
        if repo_url.endswith('/'):
            repo_url = repo_url[:-1]
        
        parts = repo_url.replace('https://github.com/', '').split('/')
        if len(parts) != 2:
            raise ValueError(f"Invalid GitHub URL format: {repo_url}")
            
        return parts[0], parts[1]  # owner, repo
    
    def _get_commit_cache_path(self, owner: str, repo: str, commit_hash: str) -> Path:
        """获取commit缓存路径"""
        cache_name = f"{owner}_{repo}_{commit_hash[:8]}"
        return self.cache_dir / cache_name
    
    def get_commit_info(self, repo_url: str, commit_hash: str) -> Optional[Dict]:
        """通过API获取commit信息"""
        try:
            owner, repo = self._parse_github_url(repo_url)
            api_url = f"{self.api_base}/repos/{owner}/{repo}/commits/{commit_hash}"
            
            self.logger.info(f"📡 Fetching commit info: {owner}/{repo}@{commit_hash[:8]}")
            
            response = requests.get(api_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                commit_info = response.json()
                self.logger.info(f"✅ Got commit info for {commit_hash[:8]}")
                return commit_info
            elif response.status_code == 404:
                self.logger.error(f"❌ Commit not found: {commit_hash}")
                return None
            else:
                self.logger.error(f"❌ API error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Failed to get commit info: {e}")
            return None
    
    def download_commit_archive(self, repo_url: str, commit_hash: str, target_dir: Path) -> bool:
        """下载特定commit的归档文件"""
        try:
            owner, repo = self._parse_github_url(repo_url)
            
            # 检查缓存
            cache_path = self._get_commit_cache_path(owner, repo, commit_hash)
            if cache_path.exists():
                self.logger.info(f"📦 Using cached archive: {cache_path}")
                return self._extract_cached_archive(cache_path, target_dir)
            
            # 下载归档
            archive_url = f"{self.api_base}/repos/{owner}/{repo}/zipball/{commit_hash}"
            
            self.logger.info(f"⬇️ Downloading archive: {owner}/{repo}@{commit_hash[:8]}")
            
            response = requests.get(archive_url, headers=self.headers, timeout=600, stream=True)
            
            if response.status_code != 200:
                self.logger.error(f"❌ Download failed {response.status_code}: {response.text}")
                return False
            
            # 保存到缓存
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            archive_file = cache_path.with_suffix('.zip')
            
            with open(archive_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"💾 Saved archive to cache: {archive_file}")
            
            # 解压到目标目录
            return self._extract_archive(archive_file, target_dir, cache_path)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to download archive: {e}")
            return False
    
    def _extract_archive(self, archive_file: Path, target_dir: Path, cache_path: Path) -> bool:
        """解压归档文件"""
        try:
            # 清理目标目录
            if target_dir.exists():
                import shutil
                shutil.rmtree(target_dir)
            
            # 解压
            with zipfile.ZipFile(archive_file, 'r') as zip_ref:
                zip_ref.extractall(cache_path)
            
            # 找到解压后的目录（GitHub归档包含一个子目录）
            extracted_dirs = [d for d in cache_path.iterdir() if d.is_dir()]
            if not extracted_dirs:
                self.logger.error("❌ No directory found in archive")
                return False
            
            source_dir = extracted_dirs[0]
            
            # 移动到目标位置
            import shutil
            shutil.move(str(source_dir), str(target_dir))
            
            self.logger.info(f"✅ Extracted to: {target_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to extract archive: {e}")
            return False
    
    def _extract_cached_archive(self, cache_path: Path, target_dir: Path) -> bool:
        """使用缓存的归档"""
        try:
            # 如果缓存目录不存在，尝试重新下载
            if not cache_path.exists():
                self.logger.warning(f"❌ Cache path not found: {cache_path}")
                return False
                
            cached_dirs = [d for d in cache_path.iterdir() if d.is_dir()]
            if not cached_dirs:
                # 如果没有目录，可能需要重新解压zip文件
                zip_file = cache_path.with_suffix('.zip')
                if zip_file.exists():
                    self.logger.info(f"📦 Re-extracting cached zip: {zip_file}")
                    return self._extract_archive(zip_file, target_dir, cache_path)
                else:
                    self.logger.warning("❌ No cached directory or zip file found")
                    return False
            
            source_dir = cached_dirs[0]
            
            # 清理目标目录
            if target_dir.exists():
                import shutil
                shutil.rmtree(target_dir)
            
            # 复制缓存到目标位置
            import shutil
            shutil.copytree(source_dir, target_dir)
            
            self.logger.info(f"✅ Used cached version: {target_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to use cached archive: {e}")
            return False
    
    def get_changed_files(self, repo_url: str, commit_hash: str) -> List[str]:
        """获取commit变更的文件列表"""
        commit_info = self.get_commit_info(repo_url, commit_hash)
        if not commit_info:
            return []
        
        changed_files = []
        if 'files' in commit_info:
            for file_info in commit_info['files']:
                if 'filename' in file_info:
                    changed_files.append(file_info['filename'])
        
        return changed_files
    
    def setup_cve_experiment_fast(self, cve_data: Dict, experiment_dir: Path) -> bool:
        """快速设置CVE实验（使用API）"""
        try:
            cve_id = cve_data.get('cve_id', 'unknown')
            repo_url = cve_data.get('source_repo')
            
            if not repo_url or 'github.com' not in repo_url:
                self.logger.error(f"❌ Not a GitHub repository: {repo_url}")
                return False
            
            self.logger.info(f"🚀 Setting up {cve_id} via GitHub API")
            
            # 创建实验目录
            experiment_dir.mkdir(parents=True, exist_ok=True)
            
            # 下载各个版本
            versions = [
                ('pa', cve_data.get('pa')),  # 源版本漏洞commit
                ('pb', cve_data.get('pb')),  # 源版本修复commit
                ('pc', cve_data.get('pc')),  # 目标版本漏洞commit
                ('pe', cve_data.get('pe')),  # 目标版本修复commit
            ]
            
            success_count = 0
            for version_name, commit_hash in versions:
                if not commit_hash:
                    continue
                
                version_dir = experiment_dir / version_name
                if self.download_commit_archive(repo_url, commit_hash, version_dir):
                    success_count += 1
                    self.logger.info(f"✅ {version_name}: {commit_hash[:8]}")
                else:
                    self.logger.error(f"❌ Failed to download {version_name}: {commit_hash[:8]}")
            
            if success_count >= 3:
                self.logger.info(f"🎉 Successfully set up {success_count} versions for {cve_id}")
                return True
            else:
                self.logger.error(f"❌ Only {success_count} versions downloaded for {cve_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to setup experiment via API: {e}")
            return False
    
    def get_rate_limit(self) -> Dict:
        """获取API速率限制信息"""
        try:
            response = requests.get(f"{self.api_base}/rate_limit", headers=self.headers, timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            self.logger.warning(f"Failed to get rate limit: {e}")
        
        return {}
    
    def wait_for_rate_limit(self):
        """等待速率限制重置"""
        rate_info = self.get_rate_limit()
        if 'rate' in rate_info:
            remaining = rate_info['rate'].get('remaining', 0)
            if remaining < 10:
                reset_time = rate_info['rate'].get('reset', 0)
                wait_time = reset_time - int(time.time()) + 60  # 额外等待1分钟
                if wait_time > 0:
                    self.logger.warning(f"⏰ Rate limit low, waiting {wait_time} seconds...")
                    time.sleep(wait_time) 