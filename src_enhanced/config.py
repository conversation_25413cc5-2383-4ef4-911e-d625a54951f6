#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FixMorph配置管理

安全地管理GitHub token和其他敏感配置
"""

import os
from pathlib import Path
from typing import Optional


class Config:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path.home() / ".fixmorph"
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置文件路径
        self.token_file = self.config_dir / "github_token"
        
    def get_github_token(self) -> Optional[str]:
        """获取GitHub token（按优先级：环境变量 > 配置文件）"""
        
        # 方法1: 从环境变量获取（最高优先级）
        token = os.getenv('GITHUB_TOKEN')
        if token:
            return token.strip()
        
        # 方法2: 从配置文件获取
        if self.token_file.exists():
            try:
                token = self.token_file.read_text().strip()
                if token:
                    return token
            except Exception:
                pass
        
        return None
    
    def set_github_token(self, token: str):
        """安全地保存GitHub token到配置文件"""
        try:
            # 保存到用户配置目录
            self.token_file.write_text(token.strip())
            # 设置文件权限（只有用户可读写）
            self.token_file.chmod(0o600)
            print(f"✅ GitHub token已安全保存到: {self.token_file}")
        except Exception as e:
            print(f"❌ 保存token失败: {e}")
    
    def remove_github_token(self):
        """删除保存的GitHub token"""
        try:
            if self.token_file.exists():
                self.token_file.unlink()
                print("✅ GitHub token已删除")
            else:
                print("ℹ️ 没有找到保存的token")
        except Exception as e:
            print(f"❌ 删除token失败: {e}")


# 全局配置实例
config = Config() 